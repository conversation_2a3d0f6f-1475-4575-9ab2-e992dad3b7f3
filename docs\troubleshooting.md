# Troubleshooting

This page provides solutions to common problems you might encounter while setting up or running the Pandas-AI application.

## Local Setup Issues

### `py -3.11` command not found
-   **Problem**: When running `python setup_env.py`, you get an error that the `py` command or the `-3.11` switch is not recognized.
-   **Solution**: This usually means that Python 3.11 is not installed or not correctly added to your system's PATH.
    -   Ensure you have installed Python 3.11 from the [official website](https://www.python.org/downloads/release/python-3110/).
    -   During installation, make sure to check the box that says "Add Python to PATH".
    -   Alternatively, you can use `python3.11` or `python` instead of `py -3.11` if that is the command that works in your terminal. You might need to edit the `setup_env.py` script.

### `requirements.txt` not found
-   **Problem**: The setup script fails because `requirements.txt` is missing.
-   **Solution**: The `requirements.txt` file should be present in the root of the project. If it's missing, you can generate it from `requirements.in` by running:
    ```bash
    pip install pip-tools
    pip-compile requirements.in > requirements.txt
    ```
    Then, run the setup script again.

## Docker Issues

### Docker build fails during `pip install`
-   **Problem**: The `docker build` command fails with a timeout or network error while installing Python packages.
-   **Solution**: This can be due to network issues or problems with the PyPI server.
    -   Check your internet connection.
    -   The `Dockerfile` already includes the `--timeout` flag for `pip`. You can try increasing the timeout value.
    -   If you are behind a proxy, you may need to configure Docker to use the proxy.

### Cannot connect to the application at `localhost:8501`
-   **Problem**: The Docker container is running, but you cannot access the application in your browser.
-   **Solution**:
    -   Ensure that you have mapped the port correctly with the `-p 8501:8501` flag in your `docker run` command.
    -   Check the container logs for any errors using `docker logs <container_name>`.

## Oracle Connection Errors

### `DPI-1047: Cannot locate a 64-bit Oracle Client library`
-   **Problem**: The application fails to connect to Oracle with this error.
-   **Solution**: This means the `oracledb` driver cannot find the Oracle Instant Client libraries.
    -   **Local Setup**: Ensure you have downloaded the Oracle Instant Client, and the directory containing the libraries is in your system's `PATH` or specified in the `ORACLE_CLIENT_LIB` environment variable.
    -   **Docker**: This should not happen with the provided `Dockerfile` as it handles the client setup. If you have a custom `Dockerfile`, ensure the client is installed correctly.

### Invalid credentials or service name
-   **Problem**: Connection fails with an ORA error related to invalid credentials or connection identifier.
-   **Solution**:
    -   Double-check the `OCI_HOST`, `OCI_PORT`, `OCI_USERNAME`, `OCI_PASSWORD`, and `OCI_SERVICE_NAME` environment variables.
    -   If using a `tnsnames.ora` file, ensure the service name is correctly defined in it.

---

For further help, see the [FAQ](faq.md) or open an issue on the project's repository.
