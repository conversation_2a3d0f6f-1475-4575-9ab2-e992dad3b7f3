import streamlit as st

st.set_page_config(
    layout='wide',
    page_title="Logout",
    page_icon="🚪"
)

if st.user.is_logged_in:
    st.logout()
else:
    st.markdown(
        """
        <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; min-height: 60vh;">
            <div style="background: linear-gradient(135deg, #e0e0e0 0%, #b2bec3 100%); padding: 2.5rem 2rem 2rem 2rem; border-radius: 18px; box-shadow: 0 6px 24px rgba(0,0,0,0.10); text-align: center; max-width: 420px; margin: 0 auto;">
                <div style="font-size: 2.5rem; margin-bottom: 0.7rem;">🚪</div>
                <div style="font-size: 2rem; font-weight: 700; color: #222; margin-bottom: 0.5rem;">You are logged out</div>
                <div style="font-size: 1.1rem; color: #555; margin-bottom: 1.2rem;">To use Data Insight, please log in with your Microsoft account.</div>
            </div>
        </div>
        """,
        unsafe_allow_html=True
    )
    col1, col2, col3 = st.columns([1,2,1])
    with col2:
        st.button(
            "Go to Login Page",
            on_click=lambda: st.login("microsoft"),
            use_container_width=True,
            key="login_btn_logout_page",
            help="Authenticate with your Microsoft account"
        )