"""
Dataset Comparison Module

This module provides capabilities for comparing two datasets side by side.
"""

import streamlit as st
import polars as pl
import pandas as pd
import pandas.io.formats.style as pd_style
import plotly.express as px
import plotly.graph_objects as go
import datacompy
from app_core.utils.logging import log_message
import traceback
from app_core.config import get_active_dataset


def highlight_polars_diff(pl_df: pl.DataFrame) -> pl.DataFrame:
    """
    Create a DataFrame that shows only the values that differ between _df1 and _df2 columns.
    Matching values are replaced with empty strings.
    
    Args:
        pl_df: Polars DataFrame with _df1 and _df2 suffix columns
        
    Returns:
        pl.DataFrame with only differing values shown
    """
    # Work with a copy to avoid modifying the original
    result_df = pl_df.clone()
    
    # Find all base field names (columns ending with _df1)
    base_fields = set(c[:-4] for c in pl_df.columns if c.endswith("_df1"))
    
    # For each pair of columns, show only the values that differ
    for base in base_fields:
        col1, col2 = f"{base}_df1", f"{base}_df2"
        if col1 in pl_df.columns and col2 in pl_df.columns:
            # Convert both columns to string for comparison to handle mixed types
            col1_str = pl.col(col1).cast(pl.Utf8)
            col2_str = pl.col(col2).cast(pl.Utf8)
            
            # Create a condition for where values are the same (accounting for nulls)
            same_values_condition = (
                (col1_str == col2_str) |  # String values are equal
                (pl.col(col1).is_null() & pl.col(col2).is_null())  # Both are null
            )
            
            # Replace matching values with empty strings, keep different values
            result_df = result_df.with_columns([
                pl.when(same_values_condition)
                .then(pl.lit(""))
                .otherwise(pl.col(col1).cast(pl.Utf8))  # Cast to string for display
                .alias(col1),
                
                pl.when(same_values_condition)
                .then(pl.lit(""))
                .otherwise(pl.col(col2).cast(pl.Utf8))  # Cast to string for display
                .alias(col2)
            ])
    
    return result_df


def generate_custom_report(compare_obj, df1_pandas, df2_pandas):
    """Generate a custom comparison report for two datasets using Polars for efficiency
    
    Args:
        compare_obj: datacompy.Compare object with comparison results
        df1_pandas: First DataFrame (original) in Pandas format
        df2_pandas: Second DataFrame (new) in Pandas format
        
    Returns:
        tuple: Summary stats, unique rows in each dataset, mismatched rows, and column info
    """
    # Convert to Polars for more efficient processing
    df1 = pl.from_pandas(df1_pandas) if not isinstance(df1_pandas, pl.DataFrame) else df1_pandas
    df2 = pl.from_pandas(df2_pandas) if not isinstance(df2_pandas, pl.DataFrame) else df2_pandas
    
    # Basic statistics
    df1_rows = df1.height
    df2_rows = df2.height
    
    # Column comparison
    df1_cols = set(df1.columns)
    df2_cols = set(df2.columns)
    common_cols = df1_cols.intersection(df2_cols)
    only_df1_cols = df1_cols - df2_cols
    only_df2_cols = df2_cols - df1_cols
    
    # Get mismatched data from datacompy
    only_df1_pandas = compare_obj.df1_unq_rows
    only_df2_pandas = compare_obj.df2_unq_rows
    mismatch_df_pandas = compare_obj.all_mismatch()
    
    # Convert to Polars
    only_df1 = pl.from_pandas(only_df1_pandas) if hasattr(only_df1_pandas, 'shape') else pl.DataFrame()
    only_df2 = pl.from_pandas(only_df2_pandas) if hasattr(only_df2_pandas, 'shape') else pl.DataFrame()
    mismatch_df = pl.from_pandas(mismatch_df_pandas) if hasattr(mismatch_df_pandas, 'shape') else pl.DataFrame()
    
    # Generate summary statistics
    summary = {
        'df1_shape': (df1.height, df1.width),
        'df2_shape': (df2.height, df2.width),
        'common_columns': len(common_cols),
        'columns_only_df1': list(only_df1_cols) if only_df1_cols else [],
        'columns_only_df2': list(only_df2_cols) if only_df2_cols else [],
        'rows_only_df1': only_df1.height if only_df1.height > 0 else 0,
        'rows_only_df2': only_df2.height if only_df2.height > 0 else 0,
        'mismatched_rows': mismatch_df.height if mismatch_df.height > 0 else 0,
        'matching_rows': compare_obj.intersect_rows.shape[0] if hasattr(compare_obj.intersect_rows, 'shape') else 0
    }
    
    return summary, only_df1, only_df2, mismatch_df, common_cols, only_df1_cols, only_df2_cols


def show_dataset_comparison():
    """
    Display the Dataset Comparison tab for comparing two datasets.
    """
    st.subheader("Compare Datasets")
    
    # Only show comparison UI if we have at least 2 datasets
    if len(st.session_state.lazy_datasets) < 2:
        st.warning("You need at least two datasets to perform a comparison. Please upload another dataset.")
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("📤 Upload More", use_container_width=True):
                st.switch_page("pages/1_Upload_Data.py")
        with col2:
            if st.button("🔗 Database Import", use_container_width=True):
                st.switch_page("pages/6_Database_Connections.py")

    else:
        # Dataset selection
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("Dataset 1")
            dataset1 = st.selectbox(
                "Select first dataset (source):",
                options=list(st.session_state.lazy_datasets.keys()),
                key="compare_dataset1"
            )
        
        with col2:
            st.subheader("Dataset 2")
            # Filter out the first selected dataset
            remaining_datasets = [ds for ds in st.session_state.lazy_datasets.keys() if ds != dataset1]
            dataset2 = st.selectbox(
                "Select second dataset (target):",
                options=remaining_datasets,
                key="compare_dataset2"
            )
        
        # Join column selection
        if dataset1 and dataset2:
            # Get column lists for both datasets
            # Use get_column_info() instead of get_column_names()
            df1_cols = list(st.session_state.lazy_datasets[dataset1].get_column_info()['dtypes'].keys())
            df2_cols = list(st.session_state.lazy_datasets[dataset2].get_column_info()['dtypes'].keys())
            
            # Find common columns as potential join keys
            common_cols = list(set(df1_cols).intersection(set(df2_cols)))
            
            # Let user select join columns
            join_cols = st.multiselect(
                "Select columns to join on:",
                options=common_cols,
                default=common_cols[:1] if common_cols else []
            )

            # Let user select columns to ignore
            ignore_cols = st.multiselect(
                "Select columns to ignore in the comparison (optional):",
                options=common_cols,
                default=[]
            )
            
            if join_cols:
                if st.button("Run Comparison", type="primary", use_container_width=True):
                    _run_comparison(dataset1, dataset2, join_cols, ignore_cols)
            else:
                st.warning("Please select at least one column to join on.")


def _run_comparison(dataset1, dataset2, join_cols, ignore_cols):
    """Run the dataset comparison and display results."""
    with st.spinner("Comparing datasets..."):
        try:
            # Load the full datasets
            df1 = st.session_state.lazy_datasets[dataset1].get_full_data()
            df2 = st.session_state.lazy_datasets[dataset2].get_full_data()
            
            log_message(f"Comparing datasets: {dataset1} vs {dataset2} with join columns: {join_cols} and ignored columns: {ignore_cols}", level="info")
            # Filter out ignored columns
            if ignore_cols:
                df1 = df1.drop(columns=[col.lower() for col in ignore_cols])
                df2 = df2.drop(columns=[col.lower() for col in ignore_cols])
            
            # Initialize the comparison
            compare = datacompy.Compare(
                df1,
                df2,
                join_columns=join_cols,
            )
            
            # Generate custom report
            summary, only_df1, only_df2, mismatch_df, common_cols_set, only_df1_cols, only_df2_cols = generate_custom_report(compare, df1, df2)
            
            # Display results in tabs
            comp_tab1, comp_tab2, comp_tab3 = st.tabs(["Summary", "Mismatched Rows", "Unique Rows"])
            
            with comp_tab1:
                _show_comparison_summary(summary, dataset1, dataset2, compare, common_cols_set, join_cols)
            
            with comp_tab2:
                _show_mismatched_rows(mismatch_df)
            
            with comp_tab3:
                _show_unique_rows(only_df1, only_df2, dataset1, dataset2)
            
        except Exception as e:
            st.error(f"Error comparing datasets: {str(e)}")
            log_message(f"Dataset comparison error: {str(e)}", level="error")
            log_message(traceback.format_exc(), level="error")


def _show_comparison_summary(summary, dataset1, dataset2, compare, common_cols_set, join_cols):
    """Show the comparison summary tab."""

    # Calculate match percentage based on mismatched rows vs dataset1 total rows
    total_rows_df1 = summary['df1_shape'][0]
    mismatched_rows = summary['mismatched_rows']
    match_percentage = ((total_rows_df1 - mismatched_rows - summary['rows_only_df1']) / total_rows_df1 * 100) if total_rows_df1 > 0 else 0

    if match_percentage == 100:
        st.balloons()

    st.header(f"**{match_percentage:.2f}%** Match Score")
    st.subheader(f"{total_rows_df1 - mismatched_rows - summary['rows_only_df1']} matching rows out of the {total_rows_df1:,} total rows of '{dataset1}'")
    # Create a gauge chart for match percentage
    fig = go.Figure(go.Indicator(
        mode = "gauge+number+delta",
        number = {'font': {'color': "blue"}},
        value = match_percentage,
        domain = {'x': [0, 1], 'y': [0, 1]},
        title = {'text': "Dataset Match Score"},
        delta = {'reference': 100},
        gauge = {
            'axis': {'range': [None, 100]},
            'bar': {'color': "rgba(0,0,0,0)"},
            'steps': [
                {'range': [0, 50], 'color': "lightcoral"},
                {'range': [50, 80], 'color': "lightyellow"},
                {'range': [80, 100], 'color': "lightgreen"}],
            'threshold': {
                'line': {'color': "blue", 'width': 4},
                'thickness': 1,
                'value': match_percentage}
        }
    ))
    fig.update_layout(height=300)
    st.plotly_chart(fig, use_container_width=True)

    st.divider()
    
    # Display column match percentages
    if compare and hasattr(compare, 'column_stats'):
        st.subheader("Column Match Percentages (excluding ignored columns and the ones with 100% match)")
        match_data = []
        
        # Get common columns between datasets (excluding join columns from comparison)
        comparison_cols = [col for col in common_cols_set if col not in join_cols]
        
        for item in compare.column_stats:
            m = item['match_cnt']/summary['matching_rows']
            # Display only columns that have some mismatches
            if m < 1:
                match_data.append({"Column": item['column'], "Match %": 100*m})
        
        if match_data:
            match_df = pl.DataFrame(match_data)
            match_df = match_df.sort("Match %")
            
            # Create a bar chart of match percentages
            fig = px.bar(
                match_df.to_pandas(), 
                x="Column", 
                y="Match %",
                color="Match %",
                color_continuous_scale=["red", "yellow", "green"],
                range_color=[0, 100],
            )
            fig.update_layout(height=500)
            st.plotly_chart(fig, use_container_width=True)
        else:
            st.info("No data found. Make sure you have selected the correct join columns and ignored columns (if applicable).")
    
    st.divider()

    st.subheader("Comparison Detail")
    
     # Create metrics for key comparison stats with enhanced visuals and context
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("🧩 Common Columns", summary['common_columns'], help="Columns present in both datasets")
        st.metric(f"📄 '{dataset1}' Columns", summary['df1_shape'][1])
        st.metric(f"📄 '{dataset2}' Columns", summary['df2_shape'][1])
        if summary['columns_only_df1']:
            st.info(f"**Columns only in {dataset1}:**<br>{', '.join(summary['columns_only_df1'])}")
        if summary['columns_only_df2']:
            st.info(f"**Columns only in {dataset2}:**<br>{', '.join(summary['columns_only_df2'])}")

    with col2:
        st.metric("✅ Matching Rows",total_rows_df1 - mismatched_rows - summary['rows_only_df1'], help="Rows that are identical in both datasets")
        st.metric("❌ Mismatched Rows", summary['mismatched_rows'], help="Rows that match on the join keys but with differences in other columns. Go to the 'Mismatched Rows' tab for the full list.")
        st.metric(f"🔢 '{dataset1}' Rows", summary['df1_shape'][0])
        st.metric(f"🔢 '{dataset2}' Rows", summary['df2_shape'][0])

    with col3:
        st.metric(f"Rows unique to '{dataset1}'", summary['rows_only_df1'], help=f"Rows only found in {dataset1}. Go to the 'Unique Rows' tab for the full list.")
        st.metric(f"Rows unique to '{dataset2}'", summary['rows_only_df2'], help=f"Rows only found in {dataset2}. Go to the 'Unique Rows' tab for the full list.")


def _show_mismatched_rows(mismatch_df):
    """Show the mismatched rows tab."""
    st.subheader("Mismatched Rows")
    
    if mismatch_df.height > 0:
        # Display with differences only (matching values shown as empty)
        if "_df1" in "".join(mismatch_df.columns):
            try:
                # Process with Polars and show only differences
                diff_only_df = highlight_polars_diff(mismatch_df)
                # Display the dataframe with only differences shown
                st.dataframe(diff_only_df, use_container_width=True)
            except Exception as e:
                st.error(f"Error processing differences: {str(e)}")
                # Fallback to unstyled display
                st.dataframe(mismatch_df, use_container_width=True)
        else:
            # Regular display without processing
            st.dataframe(mismatch_df, use_container_width=True)                                       
    else:
        st.success("No mismatched rows found!")
    
    log_message(f"Displayed {mismatch_df.height} mismatched rows for comparison", level="info")


def _show_unique_rows(only_df1, only_df2, dataset1, dataset2):
    """Show the unique rows tab using Polars DataFrames efficiently."""
    
    # Dataset 1 unique rows
    st.subheader(f"Rows Unique to '{dataset1}' ({only_df1.height:,} rows)")
    if only_df1.height > 0:
        st.dataframe(only_df1, use_container_width=True, hide_index=True)
    else:
        st.info(f"No rows are unique to '{dataset1}'")
    
    st.divider()
    
    # Dataset 2 unique rows
    st.subheader(f"Rows Unique to '{dataset2}' ({only_df2.height:,} rows)")
    if only_df2.height > 0:
        st.dataframe(only_df2, use_container_width=True, hide_index=True)
    else:
        st.info(f"No rows are unique to '{dataset2}'")
