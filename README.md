# Pandas-AI

**Pandas-AI** is a comprehensive Python-based platform designed for advanced data analysis, exploration, and AI-powered insights. It provides a user-friendly Streamlit web interface to interact with datasets, connect to Oracle databases, and leverage AI capabilities for data-related queries.

This project is designed to be run locally or within a Docker container, providing flexibility for development and deployment.

## Key Features

- **Interactive Data Exploration**: Upload CSV or Excel files and explore data through an intuitive Streamlit interface.
- **AI-Powered Chat**: Ask questions about your data in natural language and get AI-generated responses, including charts and data summaries.
- **Oracle Database Integration**: Seamlessly connect to Oracle databases to fetch and analyze data. Configuration can be done via a UI or environment variables.
- **Automated Environment Setup**: A simple script to create a Python 3.11 virtual environment and install all necessary dependencies.
- **Docker Support**: Includes a `Dockerfile` for building and running the application in a containerized environment.
- **Extensible**: The project structure is modular, allowing for easy extension with new pages, modules, or data connectors.

## Project Structure

The project is organized into the following main directories:

- `app_core/`: Contains the core application logic, including database connections, custom patches, and utility functions.
- `pages/`: Each file in this directory corresponds to a page in the Streamlit application.
- `dataset_storage/`: Default location for storing uploaded datasets and their metadata.
- `docs/`: Contains detailed documentation about the project, its structure, and modules.
- `static/`: CSS files for styling the application.
- `tests/`: Contains tests for the application.
- `wallet/`: Directory for Oracle Wallet files.

## Getting Started

### Prerequisites

- **Python 3.11**: Make sure Python 3.11 is installed and added to your system's PATH.
- **Oracle Client** (Optional): If you plan to connect to an Oracle database, you will need the Oracle Client or Oracle Instant Client libraries.

### Local Installation

1.  **Clone the repository:**
    ```bash
    git clone <repository-url>
    cd pandas-ai
    ```

2.  **Run the setup script:**
    This script will create a virtual environment named `.venv` and install all the required packages from `requirements.txt`.
    ```bash
    python setup_env.py
    ```

3.  **Activate the virtual environment:**
    -   **Windows:**
        ```powershell
        .venv\Scripts\activate
        ```
    -   **macOS/Linux:**
        ```bash
        source .venv/bin/activate
        ```

4.  **Run the application:**
    Once the environment is activated, start the Streamlit application:
    ```bash
    streamlit run Welcome.py
    ```

### Docker Setup

You can also run the application using Docker. The provided `Dockerfile` sets up the environment, installs dependencies, and configures the Oracle Instant Client.

1.  **Build the Docker image:**
    ```bash
    docker build -t pandas-ai .
    ```

2.  **Run the Docker container:**
    ```bash
    docker run -p 8501:8501 -v $(pwd)/dataset_storage:/app/dataset_storage pandas-ai
    ```
    This command maps the application's port to your local machine and mounts the `dataset_storage` directory to persist data.

## Configuration

### Database Connection

The application can be configured to connect to an Oracle database. The recommended way is to use the "Database Connections" page in the UI.

Alternatively, you can use a `.env` file in the project root. Create the file and add the following variables:

```env
OCI_HOST=your_oracle_host
OCI_PORT=1521
OCI_USERNAME=your_username
OCI_PASSWORD=your_password
OCI_SERVICE_NAME=your_service_name

# Optional Oracle settings
ORACLE_DB_PORT=1521
ORACLE_ENCODING=UTF-8
ORACLE_CONNECTION_TIMEOUT=30
ORACLE_THICK_MODE=False
ORACLE_CLIENT_LIB=/path/to/oracle/client/lib
ORACLE_USE_WALLET=False
ORACLE_WALLET_LOCATION=/path/to/wallet
```

### Dependency Management

This project uses `pip-tools` to manage dependencies.
- `requirements.in`: This file lists the direct dependencies of the project.
- `requirements.txt`: This file is generated from `requirements.in` and contains the pinned versions of all dependencies.

To update the dependencies, modify `requirements.in` and then run:
```bash
pip-compile requirements.in > requirements.txt
pip install -r requirements.txt
```

## Documentation

For more detailed documentation on the project's architecture, modules, and features, please refer to the `docs` directory. The main page for the documentation is `docs/index.md`.

## Troubleshooting

-   **Python Not Found**: Ensure Python 3.11 is installed correctly and that the `python` command (or `py -3.11` on Windows) is available in your terminal.
-   **`requirements.txt` not found**: Make sure the file exists in the root directory. If you are managing dependencies with `requirements.in`, generate it using `pip-compile`.
-   **Oracle Connection Errors**:
    -   Verify that your Oracle Client/Instant Client is installed and the path is correct if you are using `ORACLE_CLIENT_LIB`.
    -   Check that your credentials and connection details in the `.env` file or the UI are correct.
    -   If using a wallet, ensure the `wallet` directory and its contents are correctly placed and configured.
