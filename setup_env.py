import os
import subprocess
import sys

def create_virtual_env():
    """Creates a virtual environment using Python 3.11."""
    venv_command = ["py", "-3.11", "-m", "venv", ".venv"]
    print("Creating virtual environment...")
    subprocess.run(venv_command, check=True)
    print("Virtual environment created successfully.")

def install_requirements():
    """Installs requirements from requirements.txt."""
    pip_executable = os.path.join(".venv", "Scripts", "pip")
    requirements_file = "requirements.txt"
    if not os.path.exists(requirements_file):
        print(f"{requirements_file} not found. Skipping requirements installation.")
        return
    print("Installing requirements...")
    subprocess.run([pip_executable, "install", "-r", requirements_file], check=True)
    print("Requirements installed successfully.")

if __name__ == "__main__":
    try:
        create_virtual_env()
        install_requirements()
    except subprocess.CalledProcessError as e:
        print(f"An error occurred: {e}")
        sys.exit(1)
