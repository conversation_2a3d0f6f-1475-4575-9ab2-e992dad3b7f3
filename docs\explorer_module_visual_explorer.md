# Visual Explorer Module

The Visual Explorer module enables advanced data visualization and charting, using the library pygwalker

## Features

- Create custom charts and plots
- Explore data visually
- Export visualizations for reports

This module is ideal for uncovering trends and patterns in your data.

---

## Developer Notes

- **Visualization Libraries**: Integrates with Plotly, Matplotlib, or Seaborn for flexible charting.
- **User Customization**: Exposes chart options via the UI for user-driven exploration.
- **Extensibility**: New chart types or libraries can be added with minimal refactoring.
