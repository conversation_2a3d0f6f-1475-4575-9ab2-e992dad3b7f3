"""
<PERSON><PERSON><PERSON> for fixing chart display issues in PandasAI by forcing it to use Plotly.
This script ensures charts are displayed properly in the Streamlit interface.
"""
import os
import logging
import pandas as pd
from pandasai.core.prompts import get_chat_prompt_for_sql

# Flag to track if patches have been applied
_patches_applied = False

def get_chat_prompt_for_sql_with_plotly(context, **kwargs):
    """
    Custom version of get_chat_prompt_for_sql that sets viz_lib to 'plotly'.
    This forces PandasAI to use Plotly for visualizations.
    """
    # Get the original prompt
    prompt = get_chat_prompt_for_sql(context, **kwargs)

    # Add viz_lib to the props and explicitly request plotly charts
    prompt.props['viz_lib'] = 'plotly'
    prompt.props['plotting_backend'] = 'plotly'
    prompt.props['preferred_plotting_library'] = 'plotly'

    return prompt

def apply_plotly_patches():
    """
    Apply all patches to fix chart display issues by forcing Plotly usage.
    Only applies patches once to avoid redundant operations.
    """
    global _patches_applied

    # Skip if patches have already been applied
    if _patches_applied:
        return

    # Set environment variable to prevent opening windows
    os.environ['DISPLAY'] = ''  # This prevents GUI windows from opening

    # Set pandas plotting backend to plotly
    pd.options.plotting.backend = "plotly"

    # Patch the get_chat_prompt_for_sql function in pandasai.core.prompts
    import pandasai.core.prompts
    pandasai.core.prompts.get_chat_prompt_for_sql = get_chat_prompt_for_sql_with_plotly

    # Configure plotly for notebook/streamlit compatibility
    import plotly.io as pio

    # Check if 'streamlit' is in available renderers before setting it as default
    available_renderers = list(pio.renderers.keys())
    if 'streamlit' in available_renderers:
        pio.renderers.default = "streamlit"
    else:
        # Use a fallback renderer that's likely to be available
        if 'browser' in available_renderers:
            pio.renderers.default = "browser"
        elif len(available_renderers) > 0:
            # Use the first available renderer as fallback
            pio.renderers.default = available_renderers[0]

    # Mark patches as applied
    _patches_applied = True
    logging.info("Successfully patched PandasAI to use Plotly for visualizations")
