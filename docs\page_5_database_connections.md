# Database Connections Page

The Database Connections page allows users to connect Data Insight to Oracle databases for direct data access and analysis.

## Features
- Configure Oracle database connections via form or environment variables
- Test and validate connections
- Load data from connected databases for analysis

## Typical Workflow
1. Open the Database Connections page.
2. Enter your Oracle database credentials or use pre-filled environment variables.
3. Test the connection and load data as needed.

Database integration enables advanced analysis on external data sources.

---

## Developer Notes

- **Environment & UI Integration**: Supports both .env-based and interactive form-based connection, improving flexibility for different deployment scenarios.
- **Oracle Client**: Uses Oracle Instant Client and wallet files for secure, enterprise-grade connectivity.
- **Connection Abstraction**: Database logic is abstracted to allow future support for other RDBMS with minimal changes.
- **Security**: Credentials are never stored in plain text and are only used in memory for the session.
