[server]
# Increase the maximum file upload size to 2GB for large datasets
maxUploadSize = 2048

# Increase message size limit for large data transfers
maxMessageSize = 2048

# Enable WebSocket compression for better performance
enableWebsocketCompression = true

# CORS and security settings
enableCORS = false
enableXsrfProtection = false

# For development - disable CORS and XSRF protection
# In production, you should enable proper CORS settings
headless = false

[browser]
# Automatically open browser on start
gatherUsageStats = false

[theme]
# Use light theme by default for better readability with data
primaryColor = "#4e8cff"
backgroundColor = "#ffffff"
secondaryBackgroundColor = "#f0f2f6"
textColor = "#262730"

[client]
# Show error details for debugging
showErrorDetails = true

# Disable toolbar for cleaner interface
toolbarMode = "minimal"

[global]
# Development mode settings
developmentMode = false

# Suppress warnings
suppressDeprecationWarnings = true

[runner]
# Increase timeout for long-running operations
magicEnabled = true

# Memory management for better performance with large datasets
fastReruns = true
enforceSerializableSessionState = false

[logger]
# Set logging level
level = "info"
messageFormat = "%(asctime)s %(levelname)s %(message)s"