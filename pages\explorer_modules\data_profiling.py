"""
Data Profiling Module

This module provides data profiling capabilities using ydata-profiling.
"""

import streamlit as st
import time
from app_core.utils.logging import log_message
from ydata_profiling import ProfileReport
from app_core.config import get_active_dataset



def show_data_profiling():
    """
    Display the Data Profiling tab with comprehensive statistical analysis.
    """
    lazy_dataset = get_active_dataset()
    
    if not lazy_dataset:
        st.error("No dataset available for profiling")
        return
            
    st.markdown("**Select Analysis Mode:**")
    
    # Custom styled radio buttons using columns
    mode_col1, mode_col2 = st.columns(2)

    def mode_button(label, key, mode_value, icon):
        is_active = st.session_state.get('profiling_mode', 'minimal') == mode_value
        if is_active:
            st.markdown(f"""
                <div style='display: flex; justify-content: center;'>
                    <button style='color: black; border: 2px solid grey; font-weight: bold; border-radius: 0.375rem; padding: 0.5rem 1rem; width: 100%; cursor: default; font-size: 1rem;'>
                        {icon} <b>{label}</b>
                    </button>
                </div>
            """, unsafe_allow_html=True)
        else:
            if st.button(f"{icon} {label}", help="Fast profiling with essential statistics and basic visualizations" if mode_value=="minimal" else "Comprehensive profiling with advanced statistics, correlations, and detailed visualizations", use_container_width=True, key=key):
                st.session_state.profiling_mode = mode_value
                st.rerun()

    with mode_col1:
        mode_button("Quick Analysis", "minimal_mode", "minimal", "🔬")
    with mode_col2:
        mode_button("Deep Analysis", "detailed_mode", "detailed", "🔍")

    # Default to minimal if no selection
    if 'profiling_mode' not in st.session_state:
        st.session_state.profiling_mode = "minimal"
    
        
    # Generate Report Button with enhanced styling
    cols_center = st.columns([1, 2, 1])
    generate_report = cols_center[1].button(
        "🚀 Generate Profiling Report",
        help=f"Generate a comprehensive {st.session_state.profiling_mode} analysis report",
        use_container_width=True,
        type="primary"
    )
    if generate_report:
        # Enhanced loading experience
        with st.spinner("Generating profiling report. This may take a while for large datasets..."):
            try:
                # Step 1: Data loading
                df_for_profiling = lazy_dataset.get_full_data()
                # Step 2: Data preprocessing (if any)
                # Step 3: Generating profile
                profile_config = {
                    "title": f"📈 {st.session_state.active_dataset} - Data Profile",
                    "minimal": (st.session_state.profiling_mode == "minimal"),
                    "explorative": (st.session_state.profiling_mode == "detailed")
                }
                if st.session_state.profiling_mode == "detailed":
                    profile = ProfileReport(df_for_profiling)
                    profile.config.html.navbar_show = False
                   
                else:
                    profile = ProfileReport(df_for_profiling, minimal=True)
                    profile.config.html.navbar_show = False

                st.session_state.profile_html_report = profile.to_html()
                
                # Add CSS to hide ydata branding
                custom_css = """
                <style>
                /* Hide specific ydata branding element */
                p.text-body-secondary.text-end,
                p.text-body-secondary,
                p:contains("Brought to you by"),
                p:contains("YData") {
                    display: none !important;
                }
                
                /* Hide ydata links */
                a[href*="ydata.ai"],
                a[href*="utm_source=opensource"] {
                    display: none !important;
                }
                
                /* Hide parent elements that might contain the branding */
                .text-end:has(a[href*="ydata.ai"]),
                .text-body-secondary:has(a[href*="ydata.ai"]) {
                    display: none !important;
                }
                
                /* General ydata branding hiding */
                .footer, 
                [class*="ydata"], 
                [id*="ydata"],
                footer,
                .page-footer,
                .report-footer {
                    display: none !important;
                }
                </style>
                """
                
                # Inject CSS into the HTML report
                if "<head>" in st.session_state.profile_html_report:
                    st.session_state.profile_html_report = st.session_state.profile_html_report.replace(
                        "<head>", f"<head>{custom_css}"
                    )
                else:
                    # Fallback: add CSS at the beginning of the body
                    st.session_state.profile_html_report = custom_css + st.session_state.profile_html_report
                
                st.markdown("""
                    <div style="background: white; padding: 1rem; border-radius: 10px; 
                                box-shadow: 0 4px 20px rgba(0,0,0,0.1); margin: 2rem 0;">
                        <h3 style="margin: 0 0 1rem 0; color: #1976D2; text-align: center;">
                            📊 Data Profile Report
                        </h3>
                    </div>
                    """, unsafe_allow_html=True)
                
                # Add CSS to hide ydata branding in Streamlit
                st.markdown("""
                <style>
                /* Hide ydata branding in iframe content */
                iframe {
                    border: none;
                }
                </style>
                """, unsafe_allow_html=True)
                st.components.v1.html(st.session_state.profile_html_report, height=1200, scrolling=True)
                st.markdown("<br>", unsafe_allow_html=True)
                
                # Show download button at the very bottom if report is available
                if 'profile_html_report' in st.session_state:
                    st.markdown("<br>", unsafe_allow_html=True)
                    cols_download = st.columns([1, 1, 1])
                    with cols_download[1]:
                        st.download_button(
                            label="📥 Download HTML Report",
                            data=st.session_state.profile_html_report,
                            file_name=f"{st.session_state.active_dataset}_profile_report.html",
                            mime="text/html",
                            use_container_width=True
                        )
            except Exception as e:
                # Enhanced error handling
                st.markdown("""
                <div style="background: #ffebee; padding: 1.5rem; border-radius: 10px; 
                            border-left: 4px solid #f44336; margin: 1rem 0;">
                    <h4 style="color: #c62828; margin: 0 0 0.5rem 0;">⚠️ Profiling Error</h4>
                    <p style="margin: 0; color: #666;">Unable to generate the full profiling report.</p>
                </div>
                """, unsafe_allow_html=True)
                st.error(f"**Error details:** {str(e)}")
                log_message(f"Profiling error: {str(e)}", level="error")
                # Enhanced fallback summary
                st.markdown("""
                <div style="background: white; padding: 1.5rem; border-radius: 10px; 
                            box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin: 2rem 0; border-left: 4px solid #FF9800;">
                    <h3 style="margin: 0 0 1rem 0; color: #F57C00;">📋 Alternative Data Summary</h3>
                </div>
                """, unsafe_allow_html=True)
                col_info = lazy_dataset.get_column_info()
                
                # Enhanced metrics layout
                metric_col1, metric_col2, metric_col3, metric_col4 = st.columns(4)
                
                with metric_col1:
                    st.metric(
                        label="📏 Total Rows",
                        value=f"{lazy_dataset.shape[0]:,}",
                        help="Number of records in the dataset"
                    )
                
                with metric_col2:
                    st.metric(
                        label="📋 Total Columns", 
                        value=lazy_dataset.shape[1],
                        help="Number of features/variables"
                    )
                
                with metric_col3:
                    st.metric(
                        label="🔢 Numeric Columns",
                        value=len(col_info['numeric_columns']),
                        help="Columns containing numerical data"
                    )
                
                with metric_col4:
                    total_nulls = sum(col_info['null_counts'].values())
                    st.metric(
                        label="❌ Missing Values",
                        value=f"{total_nulls:,}",
                        help="Total number of missing/null values"
                    )
                
                # Additional column type breakdown
                st.markdown("<br>", unsafe_allow_html=True)
                type_col1, type_col2, type_col3 = st.columns(3)
                
                with type_col1:
                    st.metric(
                        label="📝 Text Columns",
                        value=len(col_info['string_columns']),
                        help="Columns containing text/categorical data"
                    )
                
                with type_col2:
                    st.metric(
                        label="📅 Date Columns",
                        value=len(col_info['date_columns']),
                        help="Columns containing date/time data"
                    )
                
                with type_col3:
                    other_cols = lazy_dataset.shape[1] - len(col_info['numeric_columns']) - len(col_info['string_columns']) - len(col_info['date_columns'])
                    st.metric(
                        label="🔧 Other Types",
                        value=max(0, other_cols),
                        help="Columns with other data types"
                    )


