import atexit
import os
import shutil
import logging
import warnings
import pandasai as pai
from pandasai_openai import AzureOpenAI
from dotenv import load_dotenv
import streamlit as st
import pandas as pd
import hashlib
import time
import uuid
from app_core.storage.lazy_dataset import LazyDataset
from pathlib import Path
import tempfile
from typing import Optional

# Import file storage system only
from app_core.storage.file_storage import FileStorage

# Lazy imports for better performance
# These will be imported only when needed
_plotly_patches_applied = False
_custom_prompt_configured = False

# Configure logging with INFO level (reduced from DEBUG for better performance)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/pandasai_app.log"),
        logging.StreamHandler()
    ]
)

# Set PandasAI logger to INFO level (reduced from DEBUG for better performance)
logging.getLogger('pandasai').setLevel(logging.INFO)

logger = logging.getLogger(__name__)

# Global storage instances per tab
_storage_instances = {}

def get_file_storage() -> FileStorage:
    """Get or create file storage instance with tab-specific isolation"""
    global _storage_instances
    
    # Get tab-specific session ID
    tab_session_id = _get_session_id()
    
    # Return existing storage for this tab if it exists
    if tab_session_id in _storage_instances:
        return _storage_instances[tab_session_id]
    
    try:
        # Create tab-specific storage directory
        temp_dir = Path(tempfile.gettempdir()) / f"pandas_ai_{tab_session_id}"
        
        storage = FileStorage(str(temp_dir))
        _storage_instances[tab_session_id] = storage
        
        # Register cleanup for this tab session
        _register_session_cleanup()
        
        logger.info(f"Created tab-specific storage for session: {tab_session_id}")
        return storage
        
    except Exception as e:
        logger.error(f"Error creating file storage: {e}")
        # Fallback with random ID
        import uuid
        fallback_id = str(uuid.uuid4())[:8]
        fallback_dir = os.path.join(tempfile.gettempdir(), f"pandas_ai_fallback_{fallback_id}")
        storage = FileStorage(fallback_dir)
        _storage_instances[tab_session_id] = storage
        return storage

def _get_session_id() -> str:
    """Get or create a unique tab-specific identifier"""
    # Generate a unique tab identifier that's different for each browser tab
    # This ensures dataset isolation between tabs
    if 'tab_session_id' not in st.session_state:
        import uuid
        import time
        # Use timestamp + random UUID to ensure uniqueness across tabs
        timestamp = int(time.time() * 1000)  # milliseconds
        unique_id = str(uuid.uuid4())[:8]
        st.session_state.tab_session_id = f"tab_{timestamp}_{unique_id}"
    
    return st.session_state.tab_session_id

def _register_session_cleanup():
    """Register cleanup that's more graceful than atexit"""
    # Only register once per session
    if not hasattr(st.session_state, 'cleanup_registered'):
        st.session_state.cleanup_registered = True
        
        # Use Streamlit's session end detection
        def on_session_end():
            cleanup_on_exit()
        
        # Register with atexit as backup but with context checking
        def safe_cleanup_on_exit():
            try:
                # Check if we're in a Streamlit context before cleanup
                if hasattr(st, '_get_context') and st._get_context() is not None:
                    cleanup_on_exit()
                else:
                    # Silent cleanup without Streamlit context
                    _silent_cleanup_on_exit()
            except Exception:
                # If all else fails, do a minimal silent cleanup
                _silent_cleanup_on_exit()
        
        atexit.register(safe_cleanup_on_exit)

def _apply_monkey_patches():
    """Apply all monkey patches to PandasAI components"""
    logging.info("Applying monkey patches to PandasAI components")

    # 1. Patch for custom response handling
    try:
        from app_core.custom_patches import parser as custom_response_parser
        from pandasai.core.response import parser as original_response_parser
        original_response_parser.ResponseParser.parse = custom_response_parser.ResponseParser.parse
        original_response_parser.ResponseParser._generate_response = custom_response_parser.ResponseParser._generate_response
        original_response_parser.ResponseParser._validate_response = custom_response_parser.ResponseParser._validate_response
        logging.info("Custom ResponseParser patch applied")
    except Exception as e:
        logging.error(f"Failed to apply ResponseParser patch: {str(e)}")

    # 2. Patch for unique table names in DataFrame
    try:
        from pandasai.dataframe.base import DataFrame
        DataFrame._calculate_column_hash = lambda self: hashlib.md5((",".join(self.columns) + f"{time.time()}_{uuid.uuid4()}").encode()).hexdigest()
        logging.info("Custom DataFrame patch applied successfully")
    except Exception as e:
        logging.error(f"Failed to apply DataFrame patch: {str(e)}")

    # 3. Patch for custom prompt template
    try:
        custom_prompt_path = os.path.join(os.getcwd(), 'custom_prompt.tmpl')
        with open(custom_prompt_path, 'r') as f:
            custom_prompt_content = f.read()

        # First check if we're in a Docker environment - look for installed packages directly
        prompt_template_paths = [
            # Docker paths (no virtual env in container)
            '/usr/local/lib/python3.11/site-packages/pandasai/core/prompts/templates/generate_python_code_with_sql.tmpl',
            # Standard system-wide installation path
            '/usr/lib/python3/dist-packages/pandasai/core/prompts/templates/generate_python_code_with_sql.tmpl',
            # Add path for potential Windows container
            'C:/Python311/Lib/site-packages/pandasai/core/prompts/templates/generate_python_code_with_sql.tmpl'
        ]

        # Check virtual environment paths if we're not in Docker
        venv_path = os.environ.get('VIRTUAL_ENV')
        if venv_path:
            # Add the virtual environment path
            prompt_template_paths.insert(0, os.path.join(venv_path, 'Lib', 'site-packages', 'pandasai', 'core', 'prompts', 'templates', 'generate_python_code_with_sql.tmpl'))

        # Fallback to checking common local development locations
        possible_venv_paths = [
            os.path.join(os.path.dirname(os.path.dirname(__file__)), '.venv'),
            os.path.join(os.path.expanduser('~'), 'pandas-ai', '.venv')
        ]

        for venv in possible_venv_paths:
            if os.path.exists(venv):
                prompt_template_paths.append(os.path.join(venv, 'Lib', 'site-packages', 'pandasai', 'core', 'prompts', 'templates', 'generate_python_code_with_sql.tmpl'))

        # Try each path until we find one that exists
        template_found = False
        for path in prompt_template_paths:
            try:
                if os.path.exists(os.path.dirname(path)):
                    with open(path, 'w') as f:
                        f.write(custom_prompt_content)
                    logging.info(f"Custom prompt template applied successfully at {path}")
                    template_found = True
                    break
            except Exception as e:
                logging.warning(f"Could not write to {path}: {str(e)}")

        if not template_found:
            # Try to find where pandasai is installed
            import pandasai
            package_path = os.path.dirname(pandasai.__file__)
            prompt_path = os.path.join(package_path, 'core', 'prompts', 'templates', 'generate_python_code_with_sql.tmpl')

            try:
                with open(prompt_path, 'w') as f:
                    f.write(custom_prompt_content)
                logging.info(f"Custom prompt template applied successfully at {prompt_path}")
                template_found = True
            except Exception as e:
                logging.warning(f"Could not write to discovered path {prompt_path}: {str(e)}")

        if not template_found:
            logging.error("Could not find pandasai package path for prompt template patch")
    except Exception as e:
        logging.error(f"Failed to apply custom prompt template patch: {str(e)}")

def cleanup_on_exit():
    """Clean up datasets when application exits - only for current tab"""
    try:
        # Check if we're in Streamlit context
        try:
            if not hasattr(st, 'session_state') or st.session_state is None:
                # No Streamlit context available, use silent cleanup
                _silent_cleanup_on_exit()
                return
        except Exception:
            # If checking context fails, use silent cleanup
            _silent_cleanup_on_exit()
            return
        
        # Get current tab's storage instance
        storage = get_file_storage()
        tab_session_id = _get_session_id()
        
        # Clear all datasets for this tab
        datasets = storage.list_datasets()
        
        # Try to delete each dataset individually, continue on errors
        deleted_count = 0
        for dataset_info in datasets:
            try:
                # Extract dataset_id from the dataset info
                dataset_id = dataset_info.get('dataset_id') if isinstance(dataset_info, dict) else dataset_info
                storage.delete_dataset(dataset_id)
                deleted_count += 1
            except Exception as delete_error:
                # Log individual deletion errors but continue
                logger.warning(f"Could not delete dataset {dataset_id}: {delete_error}")
                continue
        
        logger.info(f"Tab {tab_session_id}: Deleted {deleted_count} of {len(datasets)} datasets")
        
        # Try to clear the data directory with retries and force
        data_dir = storage.data_dir
        if os.path.exists(data_dir):
            _force_remove_directory(data_dir)
        
        # Remove this tab's storage instance from global dict
        global _storage_instances
        if tab_session_id in _storage_instances:
            del _storage_instances[tab_session_id]
        
        logger.info(f"Tab {tab_session_id}: Application cleanup completed")
    except Exception as e:
        # If Streamlit context fails, try silent cleanup
        try:
            _silent_cleanup_on_exit()
        except Exception:
            pass  # Complete silent failure

def _force_remove_directory(directory_path, max_retries=3):
    """Force remove directory with retries and handle Windows file locks"""
    import time
    import gc
    
    for attempt in range(max_retries):
        try:
            # Force garbage collection to release file handles
            gc.collect()
            
            # Wait a bit for file handles to be released
            if attempt > 0:
                time.sleep(0.5)
            
            # Try to remove the directory
            if os.path.exists(directory_path):
                # On Windows, try to change permissions first
                if os.name == 'nt':  # Windows
                    _remove_readonly_and_delete(directory_path)
                else:
                    shutil.rmtree(directory_path)
                
                logger.info(f"Successfully cleaned up data directory: {directory_path}")
                return
                
        except Exception as e:
            if attempt == max_retries - 1:
                logger.warning(f"Could not completely clean up directory after {max_retries} attempts: {e}")
            else:
                logger.debug(f"Cleanup attempt {attempt + 1} failed: {e}, retrying...")

def _remove_readonly_and_delete(directory_path):
    """Remove read-only attributes and delete directory (Windows specific)"""
    import stat
    
    def handle_remove_readonly(func, path, exc):
        """Error handler for Windows readonly files"""
        try:
            # Make file writable and try again
            os.chmod(path, stat.S_IWRITE)
            func(path)
        except Exception:
            # If still can't delete, skip it
            pass
    
    # Use the error handler for readonly files
    shutil.rmtree(directory_path, onerror=handle_remove_readonly)

def init_session_state():
    """Initialize session state with tab-specific file storage"""

    # Apply monkey patches BEFORE initializing PandasAI components
    _apply_monkey_patches()

    try:
        if 'initialized' not in st.session_state:
            # Get tab-specific session ID
            tab_session_id = _get_session_id()
            
            # Initialize tab-specific storage
            storage = get_file_storage()
            
            # Initialize session state variables
            st.session_state.has_data = False
            st.session_state.active_dataset = None
            st.session_state.lazy_datasets = {}
            st.session_state.storage = storage
            
            # Initialize other session state variables
            st.session_state.messages = []
            st.session_state.dataset_messages = {}
            st.session_state.show_data_preview = False
            st.session_state.saved_visualizations = []
            st.session_state.current_viz_config = None
            st.session_state.show_data_popover = False
            
            # Load existing datasets for this tab only
            _load_existing_datasets()
            
            # Initialize log capture
            _init_log_capture()
            
            st.session_state.initialized = True
            logger.info(f"Tab {tab_session_id}: Session state initialized with tab-specific file storage")
            
    except Exception as e:
        logger.error(f"Error initializing session state: {str(e)}")
        st.error(f"Failed to initialize application: {str(e)}")

def _init_log_capture():
    """Initialize log capture for debugging"""
    if "log_buffer" not in st.session_state:
        # Create an in-memory string buffer to capture logs
        import io
        st.session_state.log_buffer = io.StringIO()

        # Only add the handler if it doesn't exist yet
        if "log_capture" not in st.session_state:
            try:
                # Create a new handler with the existing buffer
                from logging import StreamHandler
                
                # Create a context-aware stream handler
                class ContextAwareStreamHandler(StreamHandler):
                    def emit(self, record):
                        try:
                            # Check if we're in Streamlit context before emitting
                            if hasattr(st, '_get_context'):
                                context = st._get_context()
                                if context is not None:
                                    super().emit(record)
                                # If no context, silently skip
                            else:
                                # Fallback - try to emit but catch any context errors
                                super().emit(record)
                        except Exception:
                            # Silent failure to avoid infinite loops
                            pass
                
                st.session_state.log_capture = ContextAwareStreamHandler(st.session_state.log_buffer)
                st.session_state.log_capture.setLevel(logging.INFO)  # Use INFO level instead of DEBUG

                # Format the logs
                formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
                st.session_state.log_capture.setFormatter(formatter)

                # Add the handler to the root logger and pandasai logger specifically
                logging.getLogger().addHandler(st.session_state.log_capture)
                logging.getLogger('pandasai').addHandler(st.session_state.log_capture)

                # Log initialization
                logging.info("Session state initialized with log capture")
            except Exception as e:
                # If there's an error, log it but don't crash
                print(f"Error setting up log capture in init_session_state: {str(e)}")

def _load_existing_datasets():
    """Load existing datasets from tab-specific storage"""
    try:
        tab_session_id = _get_session_id()
        storage = get_file_storage()
        datasets = storage.list_datasets()  # This returns a list
        
        for dataset_info in datasets:
            dataset_id = dataset_info['dataset_id']
            dataset_name = dataset_info['dataset_name']
            
            # Create lazy dataset wrapper
            lazy_dataset = LazyDataset(
                dataset_id=dataset_id,
                storage=storage,
                metadata=dataset_info
            )
            
            st.session_state.lazy_datasets[dataset_name] = lazy_dataset
            
            # Set first dataset as active if none set
            if not st.session_state.active_dataset:
                st.session_state.active_dataset = dataset_name
                st.session_state.has_data = True
        
        logger.info(f"Tab {tab_session_id}: Loaded {len(datasets)} existing datasets")
        
    except Exception as e:
        tab_session_id = _get_session_id()
        logger.error(f"Tab {tab_session_id}: Error loading existing datasets: {str(e)}")

def store_dataset(dataset_name: str, df, original_filename: str = None, 
                 file_size_bytes: int = None) -> str:
    """Store a dataset using tab-specific file storage"""
    try:
        tab_session_id = _get_session_id()
        logger.info(f"Tab {tab_session_id}: Starting store_dataset for '{dataset_name}'")
        storage = get_file_storage()
        
        # Store the DataFrame
        logger.info(f"Tab {tab_session_id}: Storing DataFrame with shape {df.shape}")
        dataset_id = storage.store_dataframe(
            df=df,
            dataset_name=dataset_name,
            original_filename=original_filename,
            file_size_bytes=file_size_bytes
        )
        logger.info(f"Tab {tab_session_id}: DataFrame stored with ID: {dataset_id}")
        
        # Get the stored metadata - use the correct method name
        metadata = storage.get_metadata(dataset_id)  # Changed from get_dataset_metadata
        logger.info(f"Tab {tab_session_id}: Retrieved metadata: {list(metadata.keys())}")
        
        # Create lazy dataset wrapper
        lazy_dataset = LazyDataset(
            dataset_id=dataset_id,
            storage=storage,
            metadata=metadata
        )
        logger.info(f"Tab {tab_session_id}: Created LazyDataset wrapper")
        
        # Update session state
        logger.info(f"Tab {tab_session_id}: Updating session state - current datasets: {list(st.session_state.lazy_datasets.keys())}")
        st.session_state.lazy_datasets[dataset_name] = lazy_dataset
        st.session_state.active_dataset = dataset_name
        st.session_state.has_data = True
        logger.info(f"Tab {tab_session_id}: Session state updated - new datasets: {list(st.session_state.lazy_datasets.keys())}")
        
        logger.info(f"Tab {tab_session_id}: Dataset '{dataset_name}' stored and added to session state")
        return dataset_id
        
    except Exception as e:
        tab_session_id = _get_session_id()
        logger.error(f"Tab {tab_session_id}: Error storing dataset '{dataset_name}': {str(e)}")
        raise

def get_active_dataset() -> Optional[LazyDataset]:
    """Get the currently active dataset"""
    if st.session_state.active_dataset and st.session_state.active_dataset in st.session_state.lazy_datasets:
        return st.session_state.lazy_datasets[st.session_state.active_dataset]
    return None

def get_dataset_sample(dataset_name: str, n_rows: int = 1000) -> Optional[object]:
    """Get a sample from a specific dataset"""
    if dataset_name in st.session_state.lazy_datasets:
        lazy_dataset = st.session_state.lazy_datasets[dataset_name]
        return lazy_dataset.sample(n_rows)
    return None

def remove_dataset(dataset_name: str):
    """Remove a dataset from tab-specific storage"""
    try:
        tab_session_id = _get_session_id()
        if dataset_name in st.session_state.lazy_datasets:
            lazy_dataset = st.session_state.lazy_datasets[dataset_name]
            dataset_id = lazy_dataset.dataset_id
            
            # Delete from file storage
            storage = get_file_storage()
            storage.delete_dataset(dataset_id)
            
            # Remove from session state
            del st.session_state.lazy_datasets[dataset_name]
            
            # Update active dataset
            if st.session_state.active_dataset == dataset_name:
                remaining_datasets = list(st.session_state.lazy_datasets.keys())
                st.session_state.active_dataset = remaining_datasets[0] if remaining_datasets else None
                st.session_state.has_data = len(remaining_datasets) > 0
            
            logger.info(f"Tab {tab_session_id}: Dataset '{dataset_name}' removed successfully")
            
    except Exception as e:
        tab_session_id = _get_session_id()
        logger.error(f"Tab {tab_session_id}: Error removing dataset '{dataset_name}': {str(e)}")
        raise

def clear_all_data():
    """Clear all stored data for current tab"""
    try:
        tab_session_id = _get_session_id()
        storage = get_file_storage()
        
        # Get all datasets - returns a list
        datasets = storage.list_datasets()
        
        # Delete each dataset
        for dataset_info in datasets:
            dataset_id = dataset_info['dataset_id']
            storage.delete_dataset(dataset_id)
        
        # Clear session state
        st.session_state.lazy_datasets = {}
        st.session_state.active_dataset = None
        st.session_state.has_data = False
        
        logger.info(f"Tab {tab_session_id}: All data cleared successfully")
        
    except Exception as e:
        tab_session_id = _get_session_id()
        logger.error(f"Tab {tab_session_id}: Error clearing data: {str(e)}")
        raise

def init_llm():
    """Initialize the LLM and configure pandasai"""

    # Load environment variables from .env file
    load_dotenv()

    # Retrieve Azure OpenAI information from environment variables
    azure_endpoint = os.getenv("AZURE_ENDPOINT")
    api_token = os.getenv("AZURE_API_TOKEN")
    api_version = os.getenv("AZURE_API_VERSION")
    deployment_name = os.getenv("AZURE_DEPLOYMENT_NAME")

    # Initialize Azure OpenAI
    LLM = AzureOpenAI(
        azure_endpoint=azure_endpoint,
        api_token=api_token,
        api_version=api_version,
        deployment_name=deployment_name
    )

    # Get the current system prompt from session state
    system_prompt = ""
    if "system_prompt" in st.session_state:
        system_prompt = st.session_state.system_prompt
        logging.info(f"System prompt found in session state: {system_prompt[:100]}...")
    else:
        logging.warning("No system prompt found in session state.")

    # Ensure context exists and contains the system prompt
    if "context" not in st.session_state:
        st.session_state.context = {}

    st.session_state.context["system_prompt"] = system_prompt
    logging.info(f"System prompt set in context: {system_prompt[:100]}...")

    # Configure PandasAI with our LLM
    config_options = {
        "llm": LLM
    }

    # Apply the configuration
    pai.config.set(config_options)

    # Verify the LLM was set correctly
    current_config = pai.config.get()
    if hasattr(current_config, 'llm') and current_config.llm is not None:
        logging.info(f"LLM successfully set in global config: {type(current_config.llm)}")
    else:
        logging.error("Failed to set LLM in global config")
        # Try alternative method
        pai.config.llm = LLM
        logging.info("Tried setting LLM directly on config object")

    # Apply the custom prompt template with the system prompt embedded
    try:
        custom_prompt_path = os.path.join(os.getcwd(), 'custom_prompt.tmpl')
        with open(custom_prompt_path, 'r') as f:
            custom_prompt_content = f.read()

        # Replace the placeholder with the actual system prompt
        if system_prompt.strip():
            modified_prompt_content = custom_prompt_content.replace(
                "{{ context.system_prompt }}",
                system_prompt
            )
        else:
            # If no system prompt, remove the placeholder
            modified_prompt_content = custom_prompt_content.replace(
                "{{ context.system_prompt }}",
                ""
            )

        # Apply the modified template
        _apply_custom_prompt_template(modified_prompt_content)
        logging.info(f"Custom prompt template applied with embedded system prompt: {system_prompt[:100]}...")
    except Exception as e:
        logging.error(f"Failed to apply custom prompt template: {str(e)}")

    return LLM

def _apply_custom_prompt_template(custom_prompt_content):
    """Apply the custom prompt template to PandasAI"""
    # Find where pandasai is installed and apply the template
    import pandasai
    package_path = os.path.dirname(pandasai.__file__)
    prompt_path = os.path.join(package_path, 'core', 'prompts', 'templates', 'generate_python_code_with_sql.tmpl')

    try:
        with open(prompt_path, 'w') as f:
            f.write(custom_prompt_content)
        logging.info(f"Custom prompt template written to {prompt_path}")
    except Exception as e:
        logging.error(f"Failed to write custom prompt template to {prompt_path}: {str(e)}")
        # Try alternative paths
        prompt_template_paths = [
            '/usr/local/lib/python3.11/site-packages/pandasai/core/prompts/templates/generate_python_code_with_sql.tmpl',
            '/usr/lib/python3/dist-packages/pandasai/core/prompts/templates/generate_python_code_with_sql.tmpl',
        ]

        for path in prompt_template_paths:
            try:
                with open(path, 'w') as f:
                    f.write(custom_prompt_content)
                logging.info(f"Custom prompt template written to {path}")
                break
            except Exception as e2:
                logging.warning(f"Could not write to {path}: {str(e2)}")
                continue

def get_current_tab_id() -> str:
    """Get the current tab session ID for debugging purposes"""
    return _get_session_id()

def get_tab_storage_info() -> dict:
    """Get information about current tab's storage for debugging"""
    tab_session_id = _get_session_id()
    storage = get_file_storage()
    
    return {
        'tab_session_id': tab_session_id,
        'storage_dir': storage.data_dir,
        'datasets_count': len(storage.list_datasets()),
        'active_dataset': st.session_state.get('active_dataset'),
        'lazy_datasets': list(st.session_state.get('lazy_datasets', {}).keys())
    }

def _silent_cleanup_on_exit():
    """Clean up without using Streamlit context (for atexit scenarios)"""
    try:
        import tempfile
        import glob
        
        # Clean up temporary directories without logging to avoid context issues
        temp_base = tempfile.gettempdir()
        tab_dirs = glob.glob(os.path.join(temp_base, "pandasai_data_tab_*"))
        
        for tab_dir in tab_dirs:
            try:
                if os.path.exists(tab_dir):
                    _force_remove_directory(tab_dir, max_retries=1)
            except Exception:
                # Silent failure - don't log anything to avoid context warnings
                pass
                
    except Exception:
        # Complete silent failure
        pass
