import re
from typing import Any, Dict, List, Union

import numpy as np
import pandas as pd

from pandasai.exceptions import InvalidOutputValueMismatch

from pandasai.core.response.base import BaseResponse
from pandasai.core.response.chart import ChartResponse
from pandasai.core.response.dataframe import DataFrameResponse
from app_core.custom_patches.multiple import MultipleResponse
from pandasai.core.response.number import NumberResponse
from pandasai.core.response.string import StringResponse


class ResponseParser:
    def parse(self, result: Union[Dict[str, Any], List[Dict[str, Any]]], last_code_executed: str = None) -> BaseResponse:
        """
        Parse the result(s) and return the appropriate response object

        Args:
            result: Either a single result dictionary or a list of result dictionaries
            last_code_executed: The last code executed to generate the result(s)

        Returns:
            BaseResponse: The appropriate response object
        """
        # Handle multiple results
        if isinstance(result, list):
            # Validate each result in the list
            for res in result:
                self._validate_response(res)
            return MultipleResponse(result, last_code_executed)

        # Handle single result (backward compatibility)
        self._validate_response(result)
        return self._generate_response(result, last_code_executed)

    def _generate_response(self, result: dict, last_code_executed: str = None):
        if result["type"] == "number":
            return NumberResponse(result["value"], last_code_executed)
        elif result["type"] == "string" or result["type"] == "text":
            return StringResponse(result["value"], last_code_executed)
        elif result["type"] == "html":
            return BaseResponse(result["value"], "html", last_code_executed)
        elif result["type"] == "dataframe":
            return DataFrameResponse(result["value"], last_code_executed)
        elif result["type"] == "plot":
            return ChartResponse(result["value"], last_code_executed)
        elif result["type"] == "figure":
            return BaseResponse(result["value"], "figure", last_code_executed)
        elif result["type"] == "multiple":
            if not isinstance(result["value"], list):
                raise InvalidOutputValueMismatch(
                    "Invalid output: Expected a list of result dictionaries for result type 'multiple'."
                )
            # Each item in the list should be a dict with 'type' and 'value'
            # Basic validation here, deeper validation can be in MultipleResponse or display logic
            for item in result["value"]:
                if not isinstance(item, dict) or "type" not in item or "value" not in item:
                    raise InvalidOutputValueMismatch(
                        "Invalid sub-item in 'multiple' response: Each item must be a dictionary with 'type' and 'value'."
                    )
            return MultipleResponse(result["value"], last_code_executed)
        else:
            raise InvalidOutputValueMismatch(f"Invalid output type: {result['type']}")

    def _validate_response(self, result: dict):
        if (
            not isinstance(result, dict)
            or "type" not in result
            or "value" not in result
        ):
            raise InvalidOutputValueMismatch(
                'Result must be in the format of dictionary of type and value like `result = {"type": ..., "value": ... , ...}`'
            )
        elif result["type"] == "number":
            if not isinstance(result["value"], (int, float, np.int64)):
                raise InvalidOutputValueMismatch(
                    "Invalid output: Expected a numeric value for result type 'number', but received a non-numeric value."
                )
        elif result["type"] == "string":
            if not isinstance(result["value"], str):
                raise InvalidOutputValueMismatch(
                    "Invalid output: Expected a string value for result type 'string' or 'text', but received a non-string value."
                )
        elif result["type"] == "dataframe":
            if not isinstance(result["value"], (pd.DataFrame, pd.Series, dict)):
                raise InvalidOutputValueMismatch(
                    "Invalid output: Expected a Pandas DataFrame or Series, but received an incompatible type."
                )
        elif result["type"] == "plot":
            # More permissive validation for plot type
            # Allow string (path/base64), dict (JSON for plotly), or any object that might be a figure
            if isinstance(result["value"], (str, dict)):
                return True

            # Check if it's a plotly figure or has to_dict method (common in plotly figures)
            if hasattr(result["value"], "to_dict") or "plotly" in repr(type(result["value"])):
                return True

            # If it's not a recognized type, raise an error
            raise InvalidOutputValueMismatch(
                "Invalid output: Expected a plot save path str, JSON dict, or plotly figure for result type 'plot', but received an incompatible type."
            )

        elif result["type"] == "html":
            # HTML content should be a string
            if not isinstance(result["value"], str):
                raise InvalidOutputValueMismatch(
                    "Invalid output: Expected an HTML string for result type 'html', but received a non-string value."
                )
            return True

        elif result["type"] == "figure":
            # More permissive check for figure types
            # Check if it's a known figure type (Plotly or Matplotlib)
            if "plotly.graph_objs._figure.Figure" in repr(type(result["value"])) or \
               "matplotlib.figure.Figure" in repr(type(result["value"])):
                return True

            # Check if it has common figure attributes/methods
            if hasattr(result["value"], "to_dict") or hasattr(result["value"], "update_layout"):
                return True

            # If it's not a recognized figure type, log a warning but allow it
            # This is more permissive to handle various figure objects
            return True

        elif result["type"] == "multiple":
            if not isinstance(result["value"], list):
                raise InvalidOutputValueMismatch(
                    "Invalid output: Expected a list for result type 'multiple'."
                )
            # Validate each sub-item's basic structure
            for item in result["value"]:
                if not isinstance(item, dict) or "type" not in item or "value" not in item:
                    raise InvalidOutputValueMismatch(
                        "Invalid sub-item in 'multiple' response: Each item must be a dictionary with 'type' and 'value'."
                    )
            return True

        # Added "text" as valid type, similar to "string"
        elif result["type"] == "text":
            if not isinstance(result["value"], str):
                raise InvalidOutputValueMismatch(
                    "Invalid output: Expected a string value for result type 'text', but received a non-string value."
                )
            return True

        return True
