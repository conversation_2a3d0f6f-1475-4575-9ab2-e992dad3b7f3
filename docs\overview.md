# Overview

Pandas-AI is a Python-based platform for advanced data analysis, visualization, and AI-powered exploration. It features a Streamlit web interface, Oracle database integration, and robust data management tools.

The application is designed to be a one-stop solution for data scientists and analysts to perform a wide range of tasks, from simple data profiling to complex, AI-driven analysis.

## Key Features

- **Data Upload and Exploration**: Users can upload datasets in various formats (e.g., CSV, Excel) and explore them using a rich user interface.
- **AI-Powered Chat**: A chat interface allows users to ask questions about their data in natural language. The AI can generate insights, create visualizations, and perform data manipulations.
- **Oracle Database Connectivity**: The application can connect to Oracle databases to fetch data for analysis. This can be configured through the UI or environment variables.
- **Modular and Extensible Architecture**: The project is structured in a modular way, making it easy to add new pages, data sources, or analysis modules.
- **Dockerized Deployment**: The application is fully containerized, allowing for easy deployment and scaling.

See [Setup &amp; Installation](setup.md) to get started.
