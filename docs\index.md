# Pandas-AI Documentation

Welcome to the documentation for Pandas-AI, an advanced data analysis and AI-powered exploration platform built on Python and Streamlit, with integrated Oracle database support.

This documentation provides a comprehensive guide for developers and users of the Pandas-AI application.

## Table of Contents

### Getting Started
- [Overview](overview.md)
- [Setup & Installation](setup.md)
- [Docker Usage](docker.md)
- [Application Structure](structure.md)

### Core Features
- [Application Pages](pages.md)
- [Explorer Modules](explorer_modules.md)
- [Custom Patches](custom_patches.md)

### Configuration
- [Database Integration](database.md)
- [Environment Variables](env_vars.md)

### Support
- [Troubleshooting](troubleshooting.md)
- [FAQ](faq.md)

---

For detailed usage and development instructions, see the sections above.
