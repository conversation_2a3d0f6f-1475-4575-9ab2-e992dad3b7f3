# Environment Variables

Pandas-AI uses environment variables for various configuration settings. These can be defined in a `.env` file in the project root or set directly in your shell or Docker environment.

## Oracle Database Configuration

These variables are used to configure the connection to an Oracle database.

-   `OCI_HOST`: The hostname or IP address of the Oracle database server.
-   `OCI_PORT`: The port number for the database listener (default: `1521`).
-   `OCI_USERNAME`: The username for the database connection.
-   `OCI_PASSWORD`: The password for the database connection.
-   `OCI_SERVICE_NAME`: The service name of the Oracle database.
-   `ORACLE_DB_PORT`: (Alternative to `OCI_PORT`) The port number for the database.
-   `ORACLE_ENCODING`: The character encoding to use for the connection (default: `UTF-8`).
-   `ORACLE_CONNECTION_TIMEOUT`: Timeout for establishing a connection, in seconds (default: `30`).
-   `ORACLE_THICK_MODE`: Set to `True` to enable thick mode for the Oracle driver (requires Oracle Client).
-   `ORACLE_CLIENT_LIB`: The path to the Oracle Client library directory.
-   `ORACLE_USE_WALLET`: Set to `True` to use an Oracle Wallet for the connection.
-   `OR<PERSON><PERSON>_WALLET_LOCATION`: The path to the directory containing the Oracle Wallet files.

## SSL and Certificate Configuration

These variables are used to manage SSL/TLS verification.

-   `REQUESTS_CA_BUNDLE`: Path to a CA bundle file for the `requests` library.
-   `SSL_CERT_FILE`: Path to a CA bundle file for the `ssl` module.
-   `PYTHONHTTPSVERIFY`: Set to `0` to disable HTTPS certificate verification for Python's HTTP clients.
-   `NODE_TLS_REJECT_UNAUTHORIZED`: Set to `0` to disable TLS certificate verification for Node.js (used by some underlying libraries).

## Streamlit Configuration

These variables control the behavior of the Streamlit server.

-   `STREAMLIT_SERVER_PORT`: The port on which the Streamlit server will run (default: `8501`).
-   `STREAMLIT_SERVER_HEADLESS`: Set to `true` to run Streamlit in headless mode (without opening a browser).
-   `STREAMLIT_BROWSER_GATHER_USAGE_STATS`: Set to `false` to disable the collection of usage statistics by Streamlit.

## Python and Pip Configuration

-   `PYTHONUNBUFFERED`: Set to `1` to ensure that Python output is sent straight to stdout without being buffered.
-   `PYTHONPATH`: A list of directories where Python should look for modules (the `Dockerfile` sets this to `/app`).
-   `PIP_DISABLE_PIP_VERSION_CHECK`: Set to `1` to disable the warning about a new version of pip being available.

---

Next, learn about [Troubleshooting](troubleshooting.md).
