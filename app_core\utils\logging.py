import logging
import time
import streamlit as st
import os

# Ensure every log line goes to the file 'pandasai_app.log' in the logs directory
LOG_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'logs')
LOG_FILE = os.path.join(LOG_DIR, 'pandasai_app.log')
os.makedirs(LOG_DIR, exist_ok=True)

_logger = logging.getLogger("pandasai_app")
if not _logger.hasHandlers():
    _logger.setLevel(logging.INFO)
    file_handler = logging.FileHandler(LOG_FILE, encoding='utf-8')
    formatter = logging.Formatter('[%(asctime)s] [%(levelname)s] %(message)s', datefmt='%Y-%m-%d %H:%M:%S')
    file_handler.setFormatter(formatter)
    _logger.addHandler(file_handler)

def log_message(message, level="info"):
    """
    Log a message to both the session state for UI display and to the standard logging system.
    
    Args:
        message (str): The message to log
        level (str): The log level ('info', 'warning', 'error', 'debug')
    """
    # Log to session state for UI display if it exists
    if hasattr(st, 'session_state') and hasattr(st.session_state, 'log_messages'):
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] [{level.upper()}] {message}"
        st.session_state.log_messages.append(log_entry)

    # Try to use the agent's logger if available
    agent_logger = None
    if hasattr(st, 'session_state') and hasattr(st.session_state, "df") and hasattr(st.session_state.df, "_agent") and \
       hasattr(st.session_state.df._agent, "_state") and hasattr(st.session_state.df._agent._state, "logger"):
        agent_logger = st.session_state.df._agent._state.logger
        if agent_logger:
            if level == "info":
                agent_logger.log(message)
            elif level == "warning":
                agent_logger.log(message, level=logging.WARNING)
            elif level == "error":
                agent_logger.log(message, level=logging.ERROR)
            elif level == "debug":
                agent_logger.log(message, level=logging.DEBUG)
    
    # Also log to the standard Python logger
    logger = logging.getLogger("pandasai_app")
    if level == "info":
        logger.info(message)
    elif level == "warning":
        logger.warning(message)
    elif level == "error":
        logger.error(message)
    elif level == "debug":
        logger.debug(message)
