"""
Visual Explorer Module

This module provides interactive data visualization capabilities using PyGWalker.
"""

import streamlit as st
from app_core.utils.logging import log_message
from pygwalker.api.streamlit import StreamlitRenderer
from app_core.config import get_active_dataset



@st.cache_resource
def get_pygwalker_renderer(viz_df, walker_key):
    """Initialize and return the PyGWalker Streamlit renderer"""
    return StreamlitRenderer(viz_df, gid=walker_key)

def show_visual_explorer():
    """
    Display the Visual Explorer tab with PyGWalker interactive visualizations.
    """
    lazy_dataset = get_active_dataset()
    
    if not lazy_dataset:
        st.error("No dataset available for visualization")
        return
    
    viz_df = lazy_dataset.get_full_data()
    
    # Hide Streamlit warning message about st.experimental_user and set min height for PyGWalker renderer
    st.markdown("""
    <style>
    .stAlert {display: none !important;}

    .pygwalker-container, 
    .pygwalker-root, 
    .pygwalker-root > div,
    div[data-testid="stVerticalBlock"] > div > div > iframe,
    iframe[title="pygwalker"],
    .pygwalker-wrapper,
    .pygwalker-app {
        min-height: 850px !important;
    }
    </style>
    """, unsafe_allow_html=True)

    # Load data for visualization
    try:
        # Initialize PyGWalker renderer
        if viz_df is not None and not viz_df.empty:
            walker_key = f"walker_{st.session_state.active_dataset}_{hash(str(viz_df.columns.tolist()))}"
            renderer = get_pygwalker_renderer(viz_df, walker_key)
            renderer.explorer(default_tab="data")
        else:
            st.warning("No data available for visualization")
    except Exception as e:
        st.error(f"Error loading data for visualization: {str(e)}")
        log_message(f"PyGWalker visualization error: {str(e)}", level="error")
        st.dataframe(viz_df, use_container_width=True)
