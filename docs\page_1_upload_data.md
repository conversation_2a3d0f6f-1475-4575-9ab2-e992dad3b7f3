# Upload Data Page

The Upload Data page allows users to upload datasets (CSV, Excel, etc.) into Data Insight for analysis.

## Features
- Upload files via the web interface
- Preview uploaded data
- Store datasets for later use

## Typical Workflow
1. Navigate to the Upload Data page.
2. Select and upload your dataset file.
3. Preview the data and confirm upload.

Uploaded datasets become available for exploration and analysis in other pages.

---

## Developer Notes

- **Streamlit File Uploader**: Uses Streamlit's file uploader widget for a simple, robust upload experience.
- **Data Storage**: Uploaded files are saved to a dedicated storage directory, decoupling upload from analysis and supporting persistence across sessions.
- **Loose Coupling**: The upload logic is kept separate from data exploration, enabling easy extension (e.g., adding new file types or validation steps).
- **Extensibility**: New preprocessing or validation logic can be added before saving files, without affecting downstream pages.
