# Logout Page

The Logout page allows users to securely end their session in Data Insight.

## Features
- Log out of the application
- Ensure session and data security

## Typical Workflow
1. Click the logout button or link.
2. Confirm logout if prompted.

Logging out helps protect your data and account security.

---

## Developer Notes

- **Session Management**: Uses Streamlit's session state and custom logic to securely clear user data on logout.
- **Security**: Ensures all sensitive information is removed from memory and session.
- **UI Feedback**: Provides immediate visual confirmation of logout for better UX.
