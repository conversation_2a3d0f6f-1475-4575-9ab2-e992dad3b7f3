import streamlit as st
import logging
import sys
import os

st.set_page_config(
    layout='wide',
    page_title="Data Explorer - Visualize and analyze your data",
    page_icon="📊"
)

if False:
# if not st.user.is_logged_in:
    st.login("microsoft")
    st.stop()
else:
        
    from app_core.utils.logging import log_message
    from app_core.utils.styling import apply_css
    from app_core.config import init_session_state


    log_message("Centralized CSS applied on Data Explorer page.")
    apply_css()
    log_message("Session state initialized for Data Explorer page.")
    init_session_state()


    # Additional CSS for better page styling
    st.markdown("""
    <style>
    /* Main content styling */
    .main .block-container, .block-container {
        padding-top: 0 !important;
        margin-top: 0 !important;
        max-width: 100% !important;
    }

    /* Navigation bar wrapper styling */
    .st-key-nav_bar_wrapper {
        background-color: var(--primary-color);
        padding: 1.5rem;
        border-radius: 0.375rem;
        margin-bottom: 2rem;
    }

    /* Remove custom green button styling to use Streamlit's default primary color */
    div[data-testid="stButton"] > button {
        box-shadow: none !important;
    }

    /* Active tab styling */
    .stButton>button.active-tab {
        background-color: #1a73e8 !important;
        color: white !important;
        border: 2px solid #185abc !important;
        font-weight: bold;
        box-shadow: 0 2px 8px rgba(26,115,232,0.15);
    }

    /* Responsive design */
    @media (max-width: 768px) {
        .main .block-container {
            padding-left: 1rem !important;
            padding-right: 1rem !important;
        }
        .nav-bar-wrapper {
            flex-direction: column;
            padding: 0.5rem;
        }
    }
    </style>
    """, unsafe_allow_html=True)

    from pages.explorer_modules import (
        show_visual_explorer,
        show_data_profiling,
        show_data_quality,
        show_dataset_comparison,
        show_data_explorer_tab
    )

    # Navigation pages configuration
    pages = ["Data Explorer", "Visual Explorer", "Data Profiling", "Data Quality", "Dataset Comparison"]

    # Initialize selected page in session state if not exists
    if 'selected_page' not in st.session_state:
        st.session_state.selected_page = "Data Explorer"

    # Remove navigation bar wrapper
    with st.container(
        key="nav_bar_wrapper",
    ):
        col1, col2, col3, col4, col5 = st.columns(5)

        def nav_button(label, key, page_name, icon):
            is_active = st.session_state.selected_page == page_name
            if is_active:
                st.markdown(f"""
                    <div style='display: flex; justify-content: center;'>
                        <button style='background-color: white; color: blue; border: 2px solid #185abc; font-weight: bold; border-radius: 0.375rem; padding: 0.5rem 1rem; width: 100%; cursor: default; font-size: 1rem;'>
                            {icon} <b>{label}</b>
                        </button>
                    </div>
                """, unsafe_allow_html=True)
            else:
                if st.button(f"{icon} {label}", key=key, use_container_width=True, type="primary"):
                    st.session_state.selected_page = page_name
                    st.rerun()

        with col1:
            nav_button("Data Explorer", "nav_data_explorer", "Data Explorer", "📊")
        with col2:
            nav_button("Visual Explorer", "nav_visual_explorer", "Visual Explorer", "📈")
        with col3:
            nav_button("Data Profiling", "nav_data_profiling", "Data Profiling", "📋")
        with col4:
            nav_button("Data Quality", "nav_data_quality", "Data Quality", "🔍")
        with col5:
            nav_button("Dataset Comparison", "nav_dataset_comparison", "Dataset Comparison", "⚖️")

    # Function mapping
    functions = {
        "Data Explorer": show_data_explorer_tab,
        "Visual Explorer": show_visual_explorer,
        "Data Profiling": show_data_profiling,
        "Data Quality": show_data_quality,
        "Dataset Comparison": show_dataset_comparison
    }

    # Execute the corresponding function based on selected page
    current_page = st.session_state.selected_page
    go_to = functions.get(current_page)
    if go_to:
        go_to()
    else:
        st.error(f"Function not found for page: {current_page}")
        show_data_explorer_tab()


    # Set up logging for this page
    logger = logging.getLogger(__name__)

    # Initialize Great Expectations related session state variables if not already initialized
    if 'expectation_suites' not in st.session_state:
        st.session_state.expectation_suites = {}

    if 'validation_results' not in st.session_state:
        st.session_state.validation_results = {}

    with st.sidebar:
        
        st.header("📊 Explorer Options")

        # Add dataset selector if multiple datasets exist
        if len(st.session_state.lazy_datasets) > 0:
            st.subheader("📁 Select Dataset")

            # Create a radio button for dataset selection
            dataset_options = list(st.session_state.lazy_datasets.keys())
            selected_dataset = st.radio(
                "Choose a dataset to explore:",
                dataset_options,
                index=dataset_options.index(st.session_state.active_dataset) if st.session_state.active_dataset in dataset_options else 0
            )

            # Update the active dataset if changed
            if selected_dataset != st.session_state.active_dataset:
                st.session_state.active_dataset = selected_dataset
                st.rerun()

