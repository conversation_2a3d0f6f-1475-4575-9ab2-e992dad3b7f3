"""
Data Explorer dashboard tab - shows comprehensive overview when datasets are available,
and empty state help when no datasets exist.
"""

import streamlit as st
from .utils import render_empty_state

def show_data_explorer_tab():
    """Show the Data Explorer dashboard with comprehensive dataset overview when datasets exist,
    or empty state help messages when no datasets are available."""
    lazy_datasets = getattr(st.session_state, 'lazy_datasets', {})
    if not lazy_datasets:
        render_empty_state()
    else:
        st.title("📊 Data Explorer Dashboard")

        st.subheader("💡 Getting Started")
        
        st.markdown("""
        **Visual Explorer**: Look into the data, visualize info on column distributions and create charts.
        
        **Data Profiling**: Generate a detailed report on your dataset's structure and statistics.
        
        **Data Quality**: Check for missing values, duplicates, and data quality issues. Make assertions on your data and verify them automatically.
        
        **Dataset Comparison**: Compare two datasets side by side to find differences.
        """)

        st.divider()
        
        # Quick Actions section
        st.subheader("🚀 Quick Actions")
        col2, col3, col4 = st.columns(3)
        
        with col2:
            if st.button("💬 AI Chat", use_container_width=True):
                st.switch_page("pages/3_Chat.py")
        with col3:
            if st.button("📤 Upload More", use_container_width=True):
                st.switch_page("pages/1_Upload_Data.py")
        with col4:
            if st.button("🔗 Database Import", use_container_width=True):
                st.switch_page("pages/6_Database_Connections.py")
