# Setup & Installation

This guide provides instructions for setting up the Pandas-AI environment on your local machine. For Docker-based setup, please refer to the [Docker Usage](docker.md) documentation.

## Prerequisites

Before you begin, ensure you have the following installed:

-   **Python 3.11**: The application is built and tested with Python 3.11. You can download it from the [official Python website](https://www.python.org/downloads/release/python-3110/).
-   **(Optional) Oracle Client**: If you intend to use the Oracle database features, you will need to have the Oracle Client or Oracle Instant Client libraries installed on your system.

## Local Installation Steps

1.  **Clone the Repository**

    First, clone the Pandas-AI repository to your local machine using Git:
    ```bash
    git clone <repository-url>
    cd pandas-ai
    ```

2.  **Create the Virtual Environment**

    The project includes a setup script that automates the creation of a Python virtual environment and the installation of dependencies. Run the script from the root of the project directory:
    ```bash
    python setup_env.py
    ```
    This will create a `.venv` directory in the project root, which will contain the Python interpreter and the required libraries.

3.  **Activate the Virtual Environment**

    Before running the application, you need to activate the virtual environment. The activation command differs based on your operating system:

    -   **On Windows:**
        ```powershell
        .venv\Scripts\activate
        ```

    -   **On macOS and Linux:**
        ```bash
        source .venv/bin/activate
        ```

    Once activated, your terminal prompt should change to indicate that you are now in the virtual environment.

4.  **Run the Application**

    With the virtual environment activated, you can start the Streamlit application:
    ```bash
    streamlit run Welcome.py
    ```
    This will launch the application in your default web browser.

## Dependency Management

This project uses `pip-tools` for managing Python dependencies. The dependencies are defined in `requirements.in`, and the exact versions are pinned in `requirements.txt`.

If you need to add or update a dependency:

1.  Modify the `requirements.in` file.
2.  Run the following command to update `requirements.txt`:
    ```bash
    pip-compile requirements.in > requirements.txt
    ```
3.  Install the updated packages:
    ```bash
    pip install -r requirements.txt
    ```

---

Next, you might want to learn about the [Application Structure](structure.md).
