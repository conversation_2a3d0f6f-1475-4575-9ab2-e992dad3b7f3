"""
Shared utilities for the Data Explorer modules.
"""

import streamlit as st

def render_empty_state():
    """Render the empty state when no data is available."""
    # Create centered layout with columns
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        # Main heading with icon
        st.markdown("""
        <div style="text-align: center;">
            <h1 style="color: #1f77b4;">
                📊 Data Explorer
            </h1>
            <p style="font-size: 1.2rem; color: #666; margin-bottom: 2rem;">
                Discover insights in your data with interactive visualizations
            </p>
        </div>
        """, unsafe_allow_html=True)

        # Getting started section
        st.markdown("""
        ### 🚀 Get Started
        
        To start exploring your data, you'll need to upload at least one dataset first.
        """)
        
        # Action buttons
        st.markdown("<br>", unsafe_allow_html=True)

        # Create two columns for buttons
        btn_col1, btn_col2 = st.columns(2)

        with btn_col1:
            if st.button("📤 Upload Your Data", type="primary", use_container_width=True):
                st.switch_page("pages/1_Upload_Data.py")
        with btn_col2:
            if st.button("📥 Import from Oracle", type="primary", use_container_width=True):
                st.switch_page("pages/6_Database_Connections.py")

        st.info("""
        1. Click **Upload Your Data** above to upload a local file
        2. Or click **Import from Oracle** to connect to your database and import data
        3. Then return here to explore your data with interactive tools
        """)

    # Add some spacing
    st.markdown("<br><br>", unsafe_allow_html=True)
