# Data Explorer Page

The Data Explorer page provides tools for browsing, filtering, and visualizing uploaded datasets.

## Features
- View and filter data tables
- Generate summary statistics
- Visualize data with charts and plots
- Access advanced explorer modules

## Typical Workflow
1. Select a dataset to explore.
2. Use filters and search to focus on relevant data.
3. Generate visualizations and summaries.

Advanced modules are available for deeper analysis (see Explorer Modules).

---

## Developer Notes

- **Modular Design**: The explorer is split into core and advanced modules (see `explorer_modules/`), making it easy to add or update features.
- **State Management**: Relies on Streamlit's session state for user selections and filters, ensuring a responsive UI.
- **Data Abstraction**: Datasets are loaded via a common interface, allowing future support for new data sources.
- **Extensibility**: New explorer modules can be registered and surfaced in the UI with minimal changes to the main page.
