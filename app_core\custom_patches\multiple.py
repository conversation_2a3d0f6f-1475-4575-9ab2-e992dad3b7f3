from typing import Any, List

from pandasai.core.response.base import BaseResponse


class MultipleResponse(BaseResponse):
    """
    Response class for handling multiple results.
    """

    def __init__(
        self,
        values: List[dict] = None,
        last_code_executed: str = None,
    ):
        """
        Initialize the MultipleResponse object

        :param values: List of result dictionaries, each with 'type' and 'value' keys
        :param last_code_executed: The last code executed to generate the values
        """
        if values is None or len(values) == 0:
            raise ValueError("Values should not be None or empty")

        # Store the original values list
        self.values = values

        # For compatibility with existing code, use the values list as the main value
        # This makes self.value and self.values point to the same list
        super().__init__(
            values,
            "multiple",
            last_code_executed
        )

    def __str__(self) -> str:
        """Return a string representation of all results."""
        return "\n".join([str(value.get("value", "")) for value in self.values])

    def __repr__(self) -> str:
        """Return a detailed string representation for debugging."""
        return f"{self.__class__.__name__}(values={self.values!r})"

    def to_dict(self) -> dict:
        """Return a dictionary representation."""
        result = super().to_dict()
        result["values"] = self.values
        return result

    def get_result_at(self, index: int) -> dict:
        """Get a specific result by index."""
        if 0 <= index < len(self.values):
            return self.values[index]
        raise IndexError(f"Result index {index} out of range (0-{len(self.values)-1})")

    def get_results_by_type(self, result_type: str) -> List[dict]:
        """Get all results of a specific type."""
        return [r for r in self.values if r.get("type") == result_type]

    @property
    def count(self) -> int:
        """Return the number of results."""
        return len(self.values)
