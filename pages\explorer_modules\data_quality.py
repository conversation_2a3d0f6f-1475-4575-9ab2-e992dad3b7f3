"""
Data Quality Module

This module provides data quality assessment and validation capabilities.
"""

import streamlit as st
import pandas as pd
import numpy as np
import json
import copy
import logging
import plotly.graph_objects as go
from datetime import datetime
from app_core.config import get_active_dataset

# Set up logging
logger = logging.getLogger(__name__)

def show_data_quality():
    """
    Display the Data Quality tab with validation and quality assessment tools.
    """
    # Data Quality tab implementation using lazy dataset
    lazy_dataset = get_active_dataset()
    
    if not lazy_dataset:
        st.error("No dataset available for quality checks")
        return
    
    # Load data for quality checks
    @st.cache_data
    def load_quality_data(dataset_name: str) -> pd.DataFrame:
        """Load data for quality checks with caching"""
        lazy_ds = st.session_state.lazy_datasets[dataset_name]
        # Always use full dataset for quality checks
        return lazy_ds.get_full_data()
    
    # Load data for quality checks
    try:
        quality_df = load_quality_data(st.session_state.active_dataset)
    except Exception as e:
        st.error(f"Error loading data for quality checks: {str(e)}")
        st.stop()
    
    # Show dataset info
    st.subheader("Dataset Information")
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Rows", f"{lazy_dataset.shape[0]:,}")
    with col2:
        st.metric("Columns", lazy_dataset.shape[1])
    with col3:
        total_nulls = quality_df.isnull().sum().sum()
        st.metric("Missing Values", f"{total_nulls:,}")
    
    # Create subtabs for different data quality operations
    quality_tab1, quality_tab2, quality_tab3 = st.tabs(["📋 Define Expectations", "🔍 Run Validations", "📊 Quality Reports"])
    
    with quality_tab1:
        _show_define_expectations_tab(quality_df)
    
    with quality_tab2:
        _show_run_validations_tab(quality_df)
    
    with quality_tab3:
        _show_quality_reports_tab()

def _show_define_expectations_tab(quality_df):
    """Show the Define Expectations tab content."""
    st.subheader("Define Data Quality Expectations")

    # Create a new expectation suite
    st.subheader("Create Expectation Suite")

    suite_name = st.text_input("Expectation Suite Name", f"{st.session_state.active_dataset}_quality_suite")

    # Column selection for expectations
    st.subheader("Select Columns for Validation")
    # Display columns with checkboxes
    columns = quality_df.columns.tolist()
    selected_columns = st.multiselect("Select columns to validate", columns, default=columns[:5])

    if selected_columns:
        st.subheader("Define Expectations")

        # Create expandable sections for different types of expectations
        with st.expander("Column Existence and Type Expectations", expanded=True):
            st.markdown("These expectations validate the structure of your data.")

            # Expect columns to exist
            st.checkbox("Expect these columns to exist", value=True, key="expect_columns_to_exist")

            # Expect column types
            st.checkbox("Expect column types to match schema", value=True, key="expect_column_types")

            if st.session_state.expect_column_types:
                st.info("Column types will be inferred from the current data.")

        with st.expander("Missing Value Expectations", expanded=True):
            st.markdown("These expectations validate the completeness of your data.")
            # For each selected column, add missing value expectations
            for column in selected_columns:
                has_nulls = quality_df[column].isnull().sum() > 0
                st.checkbox(f"Expect '{column}' values to not be null",
                           value=not has_nulls,
                           key=f"not_null_{column}")

        with st.expander("Uniqueness Expectations", expanded=True):
            st.markdown("These expectations validate the uniqueness of values in your data.")
            # For each selected column, add uniqueness expectations
            for column in selected_columns:
                unique_count = quality_df[column].nunique()
                total_count = quality_df.shape[0] - quality_df[column].isnull().sum()
                is_unique = unique_count == total_count
                st.checkbox(f"Expect '{column}' values to be unique",
                           value=is_unique,
                           key=f"unique_{column}")

        with st.expander("Value Range Expectations", expanded=True):
            st.markdown("These expectations validate the range of values in your data.")

            # For each selected numeric column, add range expectations
            numeric_columns = [col for col in selected_columns if quality_df[col].dtype.kind in 'biufc']

            if numeric_columns:
                for column in numeric_columns:
                    min_val = float(quality_df[column].min())
                    max_val = float(quality_df[column].max())

                    st.subheader(f"Range for '{column}'")
                    use_range = st.checkbox(f"Expect '{column}' values to be in range",
                                          value=True,
                                          key=f"range_{column}")

                    if use_range:
                        min_range, max_range = st.slider(
                            f"Expected range for '{column}'",
                            float(min_val - abs(min_val*0.1)),
                            float(max_val + abs(max_val*0.1)),
                            (float(min_val), float(max_val)),
                            key=f"range_slider_{column}"
                        )
                        st.write(f"Min: {min_range}, Max: {max_range}")
            else:
                st.info("No numeric columns selected for range validation.")

        # Save the expectation suite
        if st.button("Save Expectation Suite"):
            _save_expectation_suite(suite_name, selected_columns, quality_df)

def _save_expectation_suite(suite_name, selected_columns, quality_df):
    """Save the expectation suite to session state."""
    # Create a dictionary to store the expectations
    expectations = {
        "name": suite_name,
        "dataset": st.session_state.active_dataset,
        "created_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "columns": selected_columns,
        "expectations": []
    }

    # Add column existence expectations
    if st.session_state.expect_columns_to_exist:
        expectations["expectations"].append({
            "type": "expect_table_columns_to_contain_set",
            "columns": selected_columns
        })

    # Add column type expectations
    if st.session_state.expect_column_types:
        for column in selected_columns:
            dtype = str(quality_df[column].dtype)
            expectations["expectations"].append({
                "type": "expect_column_values_to_be_of_type",
                "column": column,
                "type_": dtype
            })

    # Add not null expectations
    for column in selected_columns:
        if st.session_state.get(f"not_null_{column}", False):
            expectations["expectations"].append({
                "type": "expect_column_values_to_not_be_null",
                "column": column
            })

    # Add uniqueness expectations
    for column in selected_columns:
        if st.session_state.get(f"unique_{column}", False):
            expectations["expectations"].append({
                "type": "expect_column_values_to_be_unique",
                "column": column
            })

    # Add range expectations
    numeric_columns = [col for col in selected_columns if quality_df[col].dtype.kind in 'biufc']
    for column in numeric_columns:
        if st.session_state.get(f"range_{column}", False):
            min_range = st.session_state.get(f"range_slider_{column}")[0]
            max_range = st.session_state.get(f"range_slider_{column}")[1]

            expectations["expectations"].append({
                "type": "expect_column_values_to_be_between",
                "column": column,
                "min_value": min_range,
                "max_value": max_range
            })

    # Save the expectation suite to session state
    st.session_state.expectation_suites[suite_name] = expectations

    st.success(f"Expectation suite '{suite_name}' saved successfully!")

def _show_run_validations_tab(quality_df):
    """Show the Run Validations tab content."""
    st.subheader("Run Data Validations")

    # Check if there are any expectation suites defined
    if not st.session_state.expectation_suites:
        st.warning("No expectation suites defined. Please create an expectation suite in the 'Define Expectations' tab first.")
    else:
        # Get expectation suites for this dataset
        dataset_suites = {name: suite for name, suite in st.session_state.expectation_suites.items()
                         if suite["dataset"] == st.session_state.active_dataset}

        if not dataset_suites:
            st.warning(f"No expectation suites defined for dataset '{st.session_state.active_dataset}'. Please create an expectation suite first.")
        else:
            # Select an expectation suite
            suite_options = list(dataset_suites.keys())
            selected_suite = st.selectbox(
                "Select an expectation suite to run:",
                suite_options,
                key="selected_validation_suite"
            )

            if selected_suite:
                suite = dataset_suites[selected_suite]

                # Display suite info
                st.subheader("Expectation Suite Information")
                st.markdown(f"""
                - **Suite Name**: {suite['name']}
                - **Created**: {suite['created_at']}
                - **Number of Expectations**: {len(suite['expectations'])}
                - **Columns Validated**: {len(suite['columns'])}
                """)

                # Run validation button
                if st.button("Run Validation"):
                    _run_validation(suite, selected_suite, quality_df)

def _run_validation(suite, selected_suite, quality_df):
    """Run validation for the selected suite."""
    with st.spinner("Running validation..."):
        # Create a validation result
        validation_id = f"{selected_suite}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
        validation_results = {
            "id": validation_id,
            "suite_name": selected_suite,
            "dataset": st.session_state.active_dataset,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "results": [],
            "summary": {
                "total_expectations": len(suite['expectations']),
                "passed_expectations": 0,
                "failed_expectations": 0,
                "success_percent": 0
            }
        }

        # Run each expectation
        for expectation in suite['expectations']:
            result = _validate_expectation(expectation, quality_df)
            validation_results["results"].append(result)

            # Update summary
            if result["success"]:
                validation_results["summary"]["passed_expectations"] += 1
            else:
                validation_results["summary"]["failed_expectations"] += 1

        # Calculate success percentage
        total = validation_results["summary"]["total_expectations"]
        passed = validation_results["summary"]["passed_expectations"]
        validation_results["summary"]["success_percent"] = (passed / total * 100) if total > 0 else 0

        # Save validation results
        st.session_state.validation_results[validation_id] = validation_results

        # Display validation summary
        _display_validation_summary(validation_results)

def _validate_expectation(expectation, quality_df):
    """Validate a single expectation."""
    expectation_type = expectation['type']
    result = {
        "expectation_type": expectation_type,
        "success": False,
        "details": {}
    }

    # Validate based on expectation type using quality_df
    if expectation_type == "expect_table_columns_to_contain_set":
        columns_to_check = expectation['columns']
        missing_columns = [col for col in columns_to_check if col not in quality_df.columns]
        result["success"] = len(missing_columns) == 0
        result["details"] = {
            "columns_checked": columns_to_check,
            "missing_columns": missing_columns
        }

    elif expectation_type == "expect_column_values_to_be_of_type":
        column = expectation['column']
        expected_type = expectation['type_']
        actual_type = str(quality_df[column].dtype)
        result["success"] = expected_type == actual_type
        result["details"] = {
            "column": column,
            "expected_type": expected_type,
            "actual_type": actual_type
        }

    elif expectation_type == "expect_column_values_to_not_be_null":
        column = expectation['column']
        null_count = quality_df[column].isnull().sum()
        result["success"] = null_count == 0
        result["details"] = {
            "column": column,
            "null_count": int(null_count),
            "total_rows": quality_df.shape[0]
        }

    elif expectation_type == "expect_column_values_to_be_unique":
        column = expectation['column']
        unique_count = quality_df[column].nunique()
        total_count = quality_df.shape[0] - quality_df[column].isnull().sum()
        result["success"] = unique_count == total_count
        result["details"] = {
            "column": column,
            "unique_count": int(unique_count),
            "total_non_null_count": int(total_count)
        }
    
    elif expectation_type == "expect_column_values_to_be_between":
        column = expectation['column']
        min_value = expectation['min_value']
        max_value = expectation['max_value']
        
        # Check if all values are within the range (excluding nulls)
        col_data = quality_df[column].dropna()
        if len(col_data) > 0:
            min_actual = col_data.min()
            max_actual = col_data.max()
            result["success"] = min_actual >= min_value and max_actual <= max_value
        else:
            result["success"] = True  # No data to validate
        
        result["details"] = {
            "column": column,
            "expected_min": min_value,
            "expected_max": max_value,
            "actual_min": float(min_actual) if len(col_data) > 0 else None,
            "actual_max": float(max_actual) if len(col_data) > 0 else None,
            "non_null_count": len(col_data)
        }

    return result

def _display_validation_summary(validation_results):
    """Display the validation summary and results."""
    st.subheader("Validation Summary")

    # Create metrics for the summary
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Total Expectations", validation_results["summary"]["total_expectations"])
    with col2:
        st.metric("Passed", validation_results["summary"]["passed_expectations"])
    with col3:
        st.metric("Success Rate", f"{validation_results['summary']['success_percent']:.1f}%")

    # Display detailed results
    st.subheader("Detailed Results")

    # Create tabs for passed and failed expectations
    passed_tab, failed_tab = st.tabs(["✅ Passed", "❌ Failed"])

    with passed_tab:
        passed_results = [r for r in validation_results["results"] if r["success"]]
        if not passed_results:
            st.info("No expectations passed.")
        else:
            for i, result in enumerate(passed_results):
                with st.expander(f"{i+1}. {result['expectation_type']}"):
                    st.json(result["details"])

    with failed_tab:
        failed_results = [r for r in validation_results["results"] if not r["success"]]
        if not failed_results:
            st.success("All expectations passed!")
        else:
            for i, result in enumerate(failed_results):
                with st.expander(f"{i+1}. {result['expectation_type']}", expanded=True):
                    st.json(result["details"])

def _show_quality_reports_tab():
    """Show the Quality Reports tab content."""
    st.subheader("Data Quality Reports")

    # Check if there are any validation results
    if not st.session_state.validation_results:
        st.warning("No validation results available. Please run a validation in the 'Run Validations' tab first.")
    else:
        # Filter validation results by dataset
        filtered_results = {k: v for k, v in st.session_state.validation_results.items()
                          if v["dataset"] == st.session_state.active_dataset}

        if not filtered_results:
            st.warning(f"No validation results available for dataset '{st.session_state.active_dataset}'.")
        else:
            _display_validation_history(filtered_results)

def _display_validation_history(filtered_results):
    """Display validation history and detailed reports."""
    # Display validation history
    st.subheader("Validation History")

    # Create a dataframe of validation results
    history_data = []
    for validation_id, validation in filtered_results.items():
        history_data.append({
            "Validation ID": validation_id,
            "Suite Name": validation["suite_name"],
            "Timestamp": validation["timestamp"],
            "Total Expectations": validation["summary"]["total_expectations"],
            "Passed": validation["summary"]["passed_expectations"],
            "Failed": validation["summary"]["failed_expectations"],
            "Success Rate": f"{validation['summary']['success_percent']:.1f}%"
        })

    # Convert to dataframe and display
    if history_data:
        history_df = pd.DataFrame(history_data)
        st.dataframe(history_df, use_container_width=True)

        # Select a validation to view details
        selected_validation_id = st.selectbox(
            "Select a validation to view details:",
            list(filtered_results.keys()),
            format_func=lambda x: f"{filtered_results[x]['suite_name']} ({filtered_results[x]['timestamp']})",
            key="report_validation_select"
        )

        if selected_validation_id:
            validation = filtered_results[selected_validation_id]
            _display_validation_details(validation, selected_validation_id)

def _display_validation_details(validation, validation_id):
    """Display detailed validation results with visualizations."""
    # Display validation details
    st.subheader("Validation Details")
    st.markdown(f"""
    - **Dataset**: {validation['dataset']}
    - **Suite Name**: {validation['suite_name']}
    - **Timestamp**: {validation['timestamp']}
    - **Total Expectations**: {validation['summary']['total_expectations']}
    - **Passed**: {validation['summary']['passed_expectations']}
    - **Failed**: {validation['summary']['failed_expectations']}
    - **Success Rate**: {validation['summary']['success_percent']:.1f}%
    """)

    # Create visualization of validation results
    st.subheader("Validation Results Visualization")

    # Create a pie chart of passed/failed expectations
    fig1 = go.Figure(data=[go.Pie(
        labels=['Passed', 'Failed'],
        values=[validation['summary']['passed_expectations'], validation['summary']['failed_expectations']],
        hole=.3,
        marker_colors=['#4CAF50', '#F44336']
    )])
    fig1.update_layout(title_text="Passed vs Failed Expectations")
    st.plotly_chart(fig1, use_container_width=True)

    # Create a bar chart of expectation types
    expectation_types = {}
    for result in validation['results']:
        exp_type = result['expectation_type']
        if exp_type not in expectation_types:
            expectation_types[exp_type] = {'passed': 0, 'failed': 0}

        if result["success"]:
            expectation_types[exp_type]['passed'] += 1
        else:
            expectation_types[exp_type]['failed'] += 1

    # Prepare data for the bar chart
    exp_names = list(expectation_types.keys())
    passed_counts = [expectation_types[exp]['passed'] for exp in exp_names]
    failed_counts = [expectation_types[exp]['failed'] for exp in exp_names]

    # Create the bar chart
    fig2 = go.Figure(data=[
        go.Bar(name='Passed', x=exp_names, y=passed_counts, marker_color='#4CAF50'),
        go.Bar(name='Failed', x=exp_names, y=failed_counts, marker_color='#F44336')
    ])
    fig2.update_layout(
        title_text='Results by Expectation Type',
        xaxis_title='Expectation Type',
        yaxis_title='Count',
        barmode='stack'
    )
    st.plotly_chart(fig2, use_container_width=True)

    # Export options
    st.subheader("Export Options")
    _show_export_options(validation, validation_id)

def _show_export_options(validation, validation_id):
    """Show export options for validation results."""
    # Export as JSON
    if st.button("Export Validation Results as JSON"):
        # Create a deep copy of validation results to avoid modifying the original
        validation_copy = copy.deepcopy(validation)

        # Helper function to convert NumPy types to Python native types
        def convert_numpy_types(obj):
            if isinstance(obj, dict):
                return {k: convert_numpy_types(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy_types(item) for item in obj]
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return convert_numpy_types(obj.tolist())
            elif isinstance(obj, np.bool_):
                return bool(obj)
            else:
                return obj

        # Convert all NumPy types to Python native types
        validation_json_safe = convert_numpy_types(validation_copy)

        # Convert validation results to JSON
        json_results = json.dumps(validation_json_safe, indent=2)

        # Create a download link
        st.download_button(
            label="Download JSON",
            data=json_results,
            file_name=f"validation_{validation_id}.json",
            mime="application/json"
        )

    # Generate HTML report
    if st.button("Generate HTML Report"):
        html_report = _generate_html_report(validation)
        
        # Create a download link
        st.download_button(
            label="Download HTML Report",
            data=html_report,
            file_name=f"quality_report_{validation_id}.html",
            mime="text/html"
        )

def _generate_html_report(validation):
    """Generate an HTML report for validation results."""
    
    def convert_numpy_types(obj):
        if isinstance(obj, dict):
            return {k: convert_numpy_types(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy_types(item) for item in obj]
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return convert_numpy_types(obj.tolist())
        elif isinstance(obj, np.bool_):
            return bool(obj)
        else:
            return obj
    
    html_report = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Data Quality Report - {validation['dataset']}</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            h1, h2, h3 {{ color: #2C3E50; }}
            .summary {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
            .passed {{ color: #4CAF50; }}
            .failed {{ color: #F44336; }}
            table {{ border-collapse: collapse; width: 100%; margin-bottom: 20px; }}
            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            th {{ background-color: #f2f2f2; }}
            tr:nth-child(even) {{ background-color: #f9f9f9; }}
        </style>
    </head>
    <body>
        <h1>Data Quality Report</h1>
        <div class="summary">
            <h2>Validation Summary</h2>
            <p><strong>Dataset:</strong> {validation['dataset']}</p>
            <p><strong>Suite Name:</strong> {validation['suite_name']}</p>
            <p><strong>Timestamp:</strong> {validation['timestamp']}</p>
            <p><strong>Total Expectations:</strong> {validation['summary']['total_expectations']}</p>
            <p><strong>Passed:</strong> <span class="passed">{validation['summary']['passed_expectations']}</span></p>
            <p><strong>Failed:</strong> <span class="failed">{validation['summary']['failed_expectations']}</span></p>
            <p><strong>Success Rate:</strong> {validation['summary']['success_percent']:.1f}%</p>
        </div>

        <h2>Detailed Results</h2>
        <table>
            <tr>
                <th>#</th>
                <th>Expectation Type</th>
                <th>Status</th>
                <th>Details</th>
            </tr>
    """

    # Add rows for each expectation result
    for i, result in enumerate(validation['results']):
        status = '<span class="passed">Passed</span>' if result['success'] else '<span class="failed">Failed</span>'
        # Convert NumPy types to Python native types before JSON serialization
        json_safe_details = convert_numpy_types(result['details'])
        details = json.dumps(json_safe_details, indent=2).replace('\n', '<br>').replace(' ', '&nbsp;')
        html_report += f"""
            <tr>
                <td>{i+1}</td>
                <td>{result['expectation_type']}</td>
                <td>{status}</td>
                <td><pre>{details}</pre></td>
            </tr>
        """

    # Close the HTML
    html_report += """
        </table>
        <p><em>Report generated by Data Quality Check application</em></p>
    </body>
    </html>
    """
    
    return html_report
