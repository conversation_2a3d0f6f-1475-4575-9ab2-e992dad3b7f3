# Data Quality Module

The Data Quality module assesses and visualizes data quality metrics. It uses methods similar to the great_expectations library (not used for compatibility issues)

## Features

- Detect missing or invalid values
- Visualize data quality issues
- Generate quality reports

This module helps ensure your data is clean and reliable for analysis.

---

## Developer Notes

- **Validation Logic**: Implements reusable validation functions for missing/invalid data.
- **Visualization**: Uses Streamlit and plotting libraries for clear quality reporting.
- **Extensibility**: New quality checks can be added as standalone functions or classes.
