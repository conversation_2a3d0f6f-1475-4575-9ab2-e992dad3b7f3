# Data Explorer Tab Module

The Data Explorer Tab is the main interface for browsing and interacting with datasets in Data Insight.

## Features
- Display data tables
- Apply filters and sorting
- Select columns and rows for analysis

This module is the entry point for most data exploration tasks.

---

## Developer Notes

- **Componentization**: Implements the main data table and interaction logic as a reusable function/class.
- **Performance**: Uses efficient data loading and caching to handle large datasets.
- **Integration**: Designed to work seamlessly with other modules (profiling, quality, etc.) via shared state and interfaces.
