# Application Structure

The Pandas-AI project is organized into a modular structure to promote separation of concerns and ease of maintenance. Below is a description of the key directories and files in the project.

## High-Level Overview

```
pandas-ai/
├── app_core/
│   ├── database/
│   ├── storage/
│   ├── utils/
│   └── ...
├── pages/
│   ├── explorer_modules/
│   └── ...
├── dataset_storage/
├── docs/
├── static/
├── tests/
├── wallet/
├── .env
├── compose.yaml
├── Dockerfile
├── requirements.in
├── requirements.txt
└── Welcome.py
```

## Key Directories

-   **`app_core/`**: This directory contains the core logic of the application.
    -   `config.py`: Application configuration settings.
    -   `custom_patches/`: Custom patches and extensions to the PandasAI library (see [Custom Patches](custom_patches.md)).
    -   `database/`: Modules for database connectivity, such as `oracle_manager.py`.
    -   `storage/`: Classes for managing data storage, like `file_storage.py`.
    -   `utils/`: Utility functions for logging, styling, and other common tasks.

-   **`pages/`**: Each `.py` file in this directory represents a page in the Streamlit application. Streamlit automatically creates a navigation menu based on the files in this directory.
    -   `explorer_modules/`: Contains modules used within the "Data Explorer" page, such as data profiling, quality checks, and visualization.

-   **`dataset_storage/`**: This is the default directory for storing user-uploaded datasets and their associated metadata.
    -   `datasets/`: Raw dataset files.
    -   `metadata/`: Metadata for each dataset.
    -   `cache/`: Cache for processed data.

-   **`docs/`**: Contains all the documentation for the project, written in Markdown.

-   **`static/`**: Holds static assets, such as CSS files for styling the application.

-   **`tests/`**: Contains the test suite for the application.

-   **`wallet/`**: This directory is used to store Oracle Wallet files, which are required for secure database connections.

## Key Files

-   **`Welcome.py`**: The main entry point of the Streamlit application. This is the first page that users see.

-   **`setup_env.py`**: A utility script to create a Python virtual environment and install the required dependencies.

-   **`requirements.in` / `requirements.txt`**: These files manage the Python dependencies of the project. `requirements.in` lists the high-level dependencies, and `requirements.txt` contains the pinned versions of all packages.

-   **`Dockerfile`**: The Dockerfile used to build the Docker image for the application.

-   **`compose.yaml`**: The Docker Compose file for running the application and its services.

-   **`.env`**: An optional file for storing environment variables, such as database credentials.

---

Next, learn about the [Application Pages](pages.md).
