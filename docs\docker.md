# Docker Usage

Pandas-AI is designed to be easily deployed using Docker. This document provides instructions on how to build and run the application in a Docker container.

## Using `Dockerfile`

The `Dockerfile` in the root of the project contains all the necessary instructions to build a Docker image for the application. It handles the installation of system dependencies, Python packages, and the Oracle Instant Client.

### Building the Docker Image

To build the Docker image, navigate to the project's root directory and run the following command:

```bash
docker build -t pandas-ai .
```

This will create a Docker image named `pandas-ai` on your local machine.

### Running the Docker Container

Once the image is built, you can run the application in a container:

```bash
docker run -p 8501:8501 --name pandas-ai-app pandas-ai
```

This command starts a container and maps port `8501` of the container to port `8501` on your host machine. You can access the application by navigating to `http://localhost:8501` in your web browser.

### Persisting Data

To persist the datasets uploaded to the application, you can mount a local directory to the `/app/dataset_storage` directory in the container:

```bash
docker run -p 8501:8501 -v $(pwd)/dataset_storage:/app/dataset_storage --name pandas-ai-app pandas-ai
```
*Note for Windows users: Replace `$(pwd)` with `%cd%`.*

## Using `compose.yaml`

For a more streamlined experience, you can use the `compose.yaml` file with Docker Compose. This file defines the application service, including the build context, port mappings, and environment variables.

### Running with Docker Compose

To run the application using Docker Compose, execute the following command from the project root:

```bash
docker-compose up
```

To run in detached mode, add the `-d` flag:
```bash
docker-compose up -d
```

### Stopping the Application

To stop the application, you can run:
```bash
docker-compose down
```

## Environment Variables in Docker

You can pass environment variables to the Docker container to configure the application, such as database credentials. The `compose.yaml` is configured to use a `.env` file in the project root.

See [Environment Variables](env_vars.md) for a list of all supported variables.

---

Next, learn about the [Application Structure](structure.md).
