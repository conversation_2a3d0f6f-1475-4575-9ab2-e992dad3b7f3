# Frequently Asked Questions (FAQ)

This page answers some of the most common questions about Pandas-AI.

**Q: What versions of Python are supported?**
A: The application is developed and tested with Python 3.11. While it might work with other Python 3 versions, 3.11 is the recommended and officially supported version.

**Q: How do I add a new page to the application?**
A: To add a new page, simply create a new Python file in the `pages/` directory. <PERSON><PERSON> will automatically detect the new file and add a link to it in the navigation sidebar. The filename will be used as the page title (e.g., `My_New_Page.py` will become "My New Page").

**Q: Can I connect to databases other than Oracle?**
A: Currently, the application has built-in support only for Oracle databases. However, the modular structure of the application in `app_core/database/` allows for adding new database connectors. You would need to create a new manager class for your desired database.

**Q: How do I customize the appearance of the application?**
A: The application's styling is controlled by the `static/styles.css` file. You can modify this file to change the colors, fonts, and layout of the application.

**Q: Where are the uploaded datasets stored?**
A: By default, uploaded datasets are stored in the `dataset_storage/datasets/` directory. Metadata about these datasets is stored in `dataset_storage/metadata/`. This path can be configured if needed.

**Q: Is it possible to use a different AI model for the chat feature?**
A: Yes. The AI model is configured in the application's core logic. You can modify the code to use a different model from providers like OpenAI, Hugging Face, or others. This would typically involve changing the API calls in the chat-related modules.

**Q: How can I contribute to the project?**
A: Contributions are welcome! Please refer to the project's main `README.md` file for guidelines on how to contribute, report issues, or suggest new features.

---

If your question is not answered here, please check the rest of the [documentation](index.md) or open an issue on the project's repository.
