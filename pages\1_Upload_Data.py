import streamlit as st

# MUST be first Streamlit command
st.set_page_config(
    layout='wide',
    page_title="Upload Data - Load your datasets",
    page_icon="📤"
)
if False:
# if not st.user.is_logged_in:
    st.login("microsoft")
    st.stop()
else:
        
    # Now import everything else
    import warnings
    import pandas as pd
    import polars as pl
    import io
    from app_core.utils.logging import log_message
    import traceback
    from pathlib import Path
    import time
    from datetime import datetime
    import re

    # Suppress openpyxl warnings
    warnings.filterwarnings('ignore', category=UserWarning, module='openpyxl')
    warnings.filterwarnings('ignore', message='.*Data Validation extension.*')
    warnings.filterwarnings('ignore', message='.*table.*ref.*')

    # For Excel sheet and table detection
    try:
        import openpyxl
        from openpyxl import load_workbook
        OPENPYXL_AVAILABLE = True
    except ImportError:
        OPENPYXL_AVAILABLE = False

    try:
        import chardet
        CHARDET_AVAILABLE = True
    except ImportError:
        CHARDET_AVAILABLE = False

    # Import the file storage system
    from app_core.config import (
        init_session_state, store_dataset, get_file_storage
    )
    from app_core.utils.styling import apply_css





    # Initialize session state and styling
    log_message("Session state initialized for Upload Data page.")
    init_session_state()
    log_message("Centralized CSS applied on Upload Data page.")
    apply_css()

    # Remove top padding/margin from all main containers
    st.markdown("""
    <style>
    [data-testid="stAppViewContainer"] > .main,
    [data-testid="stMainBlock"],
    [data-testid="stMainBlockContainer"],
    .block-container {
        padding-top: 0 !important;
        margin-top: 0 !important;
    }

    /* Reduce font sizes for headings and text */
    h1, .stMarkdown h1, .stTitle { font-size: 2.5rem !important; }
    h2, .stMarkdown h2, .stSubheader { font-size: 1.7rem !important; }
    h3, .stMarkdown h3 { font-size: 1.05rem !important; }

    /* Reduce font size for metrics */
    [data-testid="stMetricValue"] { font-size: 1.1rem !important; }
    [data-testid="stMetricLabel"] { font-size: 0.9rem !important; }

    /* Reduce font size for dataframe/table */
    .css-1d391kg, .stDataFrame, .stTable, .stDataFrameContainer, .stDataFrameRow, .stDataFrameCell {
        font-size: 0.85rem !important;
    }

    /* Reduce font size for text input and selectbox */
    .stTextInput > div > input, .stSelectbox > div > div { font-size: 0.95rem !important; }

    /* Reduce font size for buttons */
    .stButton > button { font-size: 0.95rem !important; }

    /* Reduce font size for radio and checkbox labels */
    .stRadio label, .stCheckbox label { font-size: 0.95rem !important; }

    </style>
    """, unsafe_allow_html=True)

    def cleanup_excel_cache():
        """Clean up old Excel cache entries to prevent session state bloat"""
        cache_keys_to_remove = []
        for key in st.session_state.keys():
            if key.startswith('excel_sheets_'):
                cache_keys_to_remove.append(key)
        
        # Keep only the most recent 5 cache entries
        if len(cache_keys_to_remove) > 5:
            cache_keys_to_remove.sort()
            for key in cache_keys_to_remove[:-5]:
                del st.session_state[key]

    # Clean up old cache entries
    cleanup_excel_cache()

    def get_file_size_mb(uploaded_file) -> float:
        """Get file size in MB"""
        if hasattr(uploaded_file, 'size'):
            return uploaded_file.size / 1024 / 1024
        return 0.0

    def validate_dataset_name(name: str) -> tuple[bool, str]:
        """Validate dataset name for file storage"""
        if not name:
            return False, "Dataset name cannot be empty"
        
        if len(name) < 2:
            return False, "Dataset name must be at least 2 characters"
        
        if len(name) > 50:
            return False, "Dataset name must be less than 50 characters"
        # Check for valid characters (alphanumeric, spaces, hyphens, underscores)
        if not re.match(r'^[a-zA-Z0-9\s\-_]+$', name):
            return False, "Dataset name can only contain letters, numbers, spaces, hyphens, and underscores"
        
        # Check if name already exists
        if name in st.session_state.lazy_datasets:
            return False, f"Dataset name '{name}' already exists. Please choose a different name."
        
        return True, ""

    def detect_encoding(uploaded_file):
        """Detect file encoding using multiple strategies"""
        try:
            import chardet
            # Read a sample for encoding detection
            uploaded_file.seek(0)
            sample = uploaded_file.read(10000)
            uploaded_file.seek(0)
            
            # Try chardet first
            detected = chardet.detect(sample)
            if detected['confidence'] > 0.8:
                return detected['encoding']
        except ImportError:
            pass
        
        # Fallback encoding detection
        encodings_to_try = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
        
        for encoding in encodings_to_try:
            try:
                uploaded_file.seek(0)
                sample = uploaded_file.read(10000)
                sample.decode(encoding)
                uploaded_file.seek(0)
                return encoding
            except (UnicodeDecodeError, AttributeError):
                continue
        
        uploaded_file.seek(0)
        return 'utf-8'  # Default fallback

    def load_csv_file(uploaded_file, **kwargs) -> tuple[pl.DataFrame, dict]:
        """Load CSV file with comprehensive error handling and multiple fallback strategies"""
        file_size_mb = get_file_size_mb(uploaded_file)
        
        # Get robust parsing option
        robust_parsing = kwargs.pop('robust_parsing', False)
        
        # Convert pandas kwargs to polars kwargs
        polars_kwargs = {}
        
        # Map common pandas args to polars
        if 'sep' in kwargs:
            polars_kwargs['separator'] = kwargs['sep']
        if 'header' in kwargs:
            if kwargs['header'] is None:
                polars_kwargs['has_header'] = False
            else:
                polars_kwargs['has_header'] = True
                if kwargs['header'] > 0:
                    polars_kwargs['skip_rows'] = kwargs['header']
        
        # Set default values if not specified
        if 'separator' not in polars_kwargs:
            polars_kwargs['separator'] = ','
        if 'has_header' not in polars_kwargs:
            polars_kwargs['has_header'] = True
        
        # Encoding detection and fallback strategy
        specified_encoding = kwargs.get('encoding', 'utf-8')
        encodings_to_try = [specified_encoding]
        
        # Add additional encodings if not already specified
        if specified_encoding != 'utf-8':
            encodings_to_try.append('utf-8')
        encodings_to_try.extend(['latin-1', 'cp1252', 'iso-8859-1'])
        
        # Remove duplicates while preserving order
        encodings_to_try = list(dict.fromkeys(encodings_to_try))
        
        # If robust parsing is enabled, try to auto-detect encoding
        if robust_parsing:
            detected_encoding = detect_encoding(uploaded_file)
            if detected_encoding and detected_encoding not in encodings_to_try:
                encodings_to_try.insert(1, detected_encoding)
        
        last_error = None
        
        # Strategy 1: Try with different encodings using strict parsing
        for encoding in encodings_to_try:
            try:
                uploaded_file.seek(0)
                polars_kwargs['encoding'] = encoding
                
                if robust_parsing:
                    # Add robust parsing options
                    polars_kwargs['ignore_errors'] = True
                    polars_kwargs['infer_schema_length'] = 10000
                    
                df = pl.read_csv(uploaded_file, **polars_kwargs)
                
                info = {
                    'file_type': 'CSV',
                    'file_size_mb': file_size_mb,
                    'rows': len(df),
                    'columns': len(df.columns),
                    'memory_mb': df.estimated_size() / (1024 * 1024),
                    'encoding_used': encoding,
                    'parsing_strategy': 'polars_standard'
                }
                
                return df, info
                
            except Exception as e:
                last_error = e
                continue
        
        # Strategy 2: Try with more lenient parsing options
        if robust_parsing:
            for encoding in encodings_to_try:
                try:
                    uploaded_file.seek(0)
                    lenient_kwargs = polars_kwargs.copy()
                    lenient_kwargs.update({
                        'encoding': encoding,
                        'ignore_errors': True,
                        'infer_schema_length': 0,  # Disable schema inference
                        'truncate_ragged_lines': True,
                    })
                    
                    df = pl.read_csv(uploaded_file, **lenient_kwargs)
                    
                    info = {
                        'file_type': 'CSV',
                        'file_size_mb': file_size_mb,
                        'rows': len(df),
                        'columns': len(df.columns),
                        'memory_mb': df.estimated_size() / (1024 * 1024),
                        'encoding_used': encoding,
                        'parsing_strategy': 'polars_lenient'
                    }
                    
                    return df, info
                    
                except Exception as e:
                    last_error = e
                    continue
        
        # Strategy 3: Fallback to pandas with error handling
        if robust_parsing:
            import pandas as pd
            for encoding in encodings_to_try:
                try:
                    uploaded_file.seek(0)
                    
                    # Convert polars separator back to pandas sep
                    pandas_kwargs = {
                        'encoding': encoding,
                        'sep': polars_kwargs.get('separator', ','),
                        'header': 0 if polars_kwargs.get('has_header', True) else None,
                        'on_bad_lines': 'skip',
                        'encoding_errors': 'replace',
                        'dtype': str,  # Read everything as strings initially
                    }
                    
                    if polars_kwargs.get('skip_rows', 0) > 0:
                        pandas_kwargs['skiprows'] = polars_kwargs['skip_rows']
                    
                    # Read with pandas
                    pd_df = pd.read_csv(uploaded_file, **pandas_kwargs)
                    
                    # Convert to polars
                    df = pl.from_pandas(pd_df)
                    
                    info = {
                        'file_type': 'CSV',
                        'file_size_mb': file_size_mb,
                        'rows': len(df),
                        'columns': len(df.columns),
                        'memory_mb': df.estimated_size() / (1024 * 1024),
                        'encoding_used': encoding,
                        'parsing_strategy': 'pandas_fallback'
                    }
                    
                    return df, info
                    
                except Exception as e:
                    last_error = e
                    continue
        
        # Strategy 4: Last resort - read as text and create single column dataframe
        if robust_parsing:
            for encoding in encodings_to_try:
                try:
                    uploaded_file.seek(0)
                    content = uploaded_file.read().decode(encoding, errors='replace')
                    lines = content.split('\n')
                    
                    # Create a single-column dataframe with raw lines
                    df = pl.DataFrame({
                        'raw_data': lines
                    })
                    
                    info = {
                        'file_type': 'CSV (Raw Text)',
                        'file_size_mb': file_size_mb,
                        'rows': len(df),
                        'columns': 1,
                        'memory_mb': df.estimated_size() / (1024 * 1024),
                        'encoding_used': encoding,
                        'parsing_strategy': 'raw_text_fallback',
                        'warning': 'File loaded as raw text due to parsing errors. Manual cleanup may be required.'
                    }
                    
                    return df, info
                    
                except Exception as e:
                    last_error = e
                    continue
        
        # If all strategies failed, raise the last error
        error_msg = f"Error reading CSV file: {str(last_error)}"
        if robust_parsing:
            error_msg += "\n\nTried multiple parsing strategies including:\n"
            error_msg += f"- Encodings: {', '.join(encodings_to_try)}\n"
            error_msg += "- Polars standard and lenient parsing\n"
            error_msg += "- Pandas fallback with error skipping\n"
            error_msg += "- Raw text parsing\n"
            error_msg += "\nSuggestions:\n"
            error_msg += "- Check if the file is truly a CSV format\n"
            error_msg += "- Try manually specifying the correct separator\n"
            error_msg += "- Consider preprocessing the file to fix formatting issues"
        raise Exception(error_msg)

    def get_excel_sheets_info(uploaded_file) -> dict:
        """Get information about all sheets in an Excel file with optimized performance"""
        try:
            # Fast approach: Use pandas for sheet detection first (faster than openpyxl)
            uploaded_file.seek(0)
            xl_file = pd.ExcelFile(uploaded_file)
            sheets_info = {}
            
            # Get basic info for all sheets quickly
            for sheet_name in xl_file.sheet_names:
                try:
                    # Quick sample read to get column count
                    df_sample = pd.read_excel(uploaded_file, sheet_name=sheet_name, nrows=0)  # Just headers
                    estimated_rows = 'Unknown'
                    
                    # Try to get row count efficiently if file is not too large
                    if hasattr(uploaded_file, 'size') and uploaded_file.size < 10 * 1024 * 1024:  # Less than 10MB
                        try:
                            df_full = pd.read_excel(uploaded_file, sheet_name=sheet_name, usecols=[0])  # Just first column
                            estimated_rows = len(df_full)
                        except Exception:
                            pass
                    
                    sheets_info[sheet_name] = {
                        'name': sheet_name,
                        'estimated_rows': estimated_rows,
                        'columns': len(df_sample.columns),
                        'tables': []  # Will be populated below if openpyxl is available
                    }
                except Exception:
                    sheets_info[sheet_name] = {
                        'name': sheet_name,
                        'estimated_rows': 'Unknown',
                        'columns': 0,
                        'tables': []
                    }
            
            # If openpyxl is available and file is small enough, try to get table information
            if OPENPYXL_AVAILABLE and hasattr(uploaded_file, 'size') and uploaded_file.size < 5 * 1024 * 1024:  # Less than 5MB
                try:
                    uploaded_file.seek(0)
                    # Try with read_only=False to access tables
                    try:
                        workbook = openpyxl.load_workbook(uploaded_file, read_only=False, data_only=True)
                        can_read_tables = True
                    except Exception:
                        uploaded_file.seek(0)
                        workbook = openpyxl.load_workbook(uploaded_file, read_only=True, data_only=True)
                        can_read_tables = False
                    
                    # Update with more accurate dimensions and table info
                    for sheet_name in workbook.sheetnames:
                        if sheet_name in sheets_info:
                            worksheet = workbook[sheet_name]
                            
                            # Update row count if we have it
                            if sheets_info[sheet_name]['estimated_rows'] == 'Unknown':
                                max_row = worksheet.max_row
                                if max_row:
                                    sheets_info[sheet_name]['estimated_rows'] = max_row                        # Get table information using the proper openpyxl approach from the website
                            if can_read_tables:
                                tables = []
                                try:
                                    if hasattr(worksheet, 'tables') and worksheet.tables:
                                        # Use the proper .tables.items() approach as shown in the website
                                        for table_name, data_boundary in worksheet.tables.items():
                                            try:
                                                # data_boundary is the cell range string like 'A1:C26'
                                                table_ref = str(data_boundary)
                                                
                                                # Try to get additional table properties
                                                table_obj = worksheet.tables.get(table_name)
                                                display_name = table_name
                                                if table_obj and hasattr(table_obj, 'displayName'):
                                                    display_name = table_obj.displayName or table_name
                                                
                                                tables.append({
                                                    'name': table_name,
                                                    'ref': table_ref,
                                                    'display_name': display_name
                                                })
                                                
                                                log_message(f"Found table {table_name} with range {table_ref}", level="debug")
                                                
                                            except Exception as table_error:
                                                log_message(f"Error processing table {table_name}: {str(table_error)}", level="debug")
                                                continue
                                        
                                        if tables:  # Only update if we found valid tables
                                            sheets_info[sheet_name]['tables'] = tables
                                            log_message(f"Sheet {sheet_name}: Found {len(tables)} tables", level="debug")
                                except Exception as e:
                                    log_message(f"Could not read tables from sheet {sheet_name}: {str(e)}", level="debug")
                    
                    workbook.close()
                    
                except Exception as e:
                    log_message(f"Could not get detailed Excel info with openpyxl: {str(e)}", level="debug")
            
            return sheets_info
            
        except Exception as e:
            log_message(f"Error getting Excel sheet info: {str(e)}", level="error")
            # Final fallback
            return {'Sheet1': {'name': 'Sheet1', 'estimated_rows': 'Unknown', 'columns': 'Unknown', 'tables': []}}

    def load_excel_sheet_or_table(uploaded_file, sheet_name=0, table_name=None) -> tuple[pl.DataFrame, dict]:
        """Load Excel file from specific sheet or table using the proper openpyxl approach"""
        try:
            file_size_mb = get_file_size_mb(uploaded_file)
            
            if table_name and OPENPYXL_AVAILABLE:
                # Load specific table from sheet using the website's approach
                try:
                    uploaded_file.seek(0)
                    workbook = load_workbook(uploaded_file, data_only=True)
                    worksheet = workbook[sheet_name]
                    table_ref = None                  # Use the proper approach to find the table by getting the table object
                    if hasattr(worksheet, 'tables') and worksheet.tables:
                        for tbl_name in worksheet.tables:
                            table_obj = worksheet.tables[tbl_name]
                            if tbl_name == table_name:
                                # Use the table object's ref property for the correct range
                                table_ref = table_obj.ref
                                log_message(f"Found table {table_name} with range {table_ref}", level="debug")
                                break
                            # Also check display name
                            if hasattr(table_obj, 'displayName') and table_obj.displayName == table_name:
                                table_ref = table_obj.ref
                                log_message(f"Found table {table_name} (display name) with range {table_ref}", level="debug")
                                break
                    
                    workbook.close()
                    
                    if table_ref:
                        # Use the website's approach: direct cell access instead of manual parsing
                        uploaded_file.seek(0)
                        try:
                            # Re-open workbook to access cells directly
                            workbook = load_workbook(uploaded_file, data_only=True)
                            worksheet = workbook[sheet_name]
                            
                            # Use the website's approach: direct cell access
                            data = worksheet[table_ref]
                            
                            # Extract the data using list comprehension (website approach)
                            content = [[cell.value for cell in row] for row in data]
                            
                            # Separate header and data
                            header = content[0]
                            rest = content[1:]
                            
                            # Create pandas DataFrame first
                            df_pandas = pd.DataFrame(rest, columns=header)
                            
                            # Convert to polars
                            df = pl.from_pandas(df_pandas)
                            
                            workbook.close()
                            
                            log_message(f"Successfully loaded table {table_name} with {len(df)} rows using website approach", level="debug")
                            
                        except Exception as e:
                            log_message(f"Could not load table using direct cell access {table_ref}: {str(e)}", level="warning")
                            # Fallback to entire sheet
                            uploaded_file.seek(0)
                            df = pl.read_excel(uploaded_file, sheet_name=sheet_name)
                    else:
                        # Table not found, fall back to reading the whole sheet
                        log_message(f"Table {table_name} not found, loading entire sheet", level="warning")
                        uploaded_file.seek(0)
                        df = pl.read_excel(uploaded_file, sheet_name=sheet_name)
                        
                except Exception as e:
                    log_message(f"Could not load table {table_name}, falling back to sheet: {str(e)}", level="warning")
                    uploaded_file.seek(0)
                    df = pl.read_excel(uploaded_file, sheet_name=sheet_name)
            else:
                # Load entire sheet
                try:
                    if isinstance(sheet_name, int):
                        df = pl.read_excel(uploaded_file, sheet_id=sheet_name)
                    else:
                        df = pl.read_excel(uploaded_file, sheet_name=sheet_name)
                except Exception:
                    # Fallback strategies
                    try:
                        df = pl.read_excel(uploaded_file, sheet_id=0)
                    except Exception:
                        result = pl.read_excel(uploaded_file)
                        if isinstance(result, dict):
                            df = list(result.values())[0]
                        else:
                            df = result
            
            # Ensure df is a DataFrame
            if isinstance(df, dict):
                df = list(df.values())[0]
            
            info = {
                'file_type': 'Excel',
                'sheet_name': sheet_name,
                'table_name': table_name,
                'file_size_mb': file_size_mb,
                'rows': len(df),
                'columns': len(df.columns),
                'memory_mb': df.estimated_size() / (1024 * 1024)
            }
            
            return df, info
            
        except Exception as e:
            raise Exception(f"Error reading Excel file: {str(e)}")

    def load_excel_file(uploaded_file, sheet_name=0) -> tuple[pl.DataFrame, dict]:
        """Load Excel file with error handling"""
        try:
            # Get file info
            file_size_mb = get_file_size_mb(uploaded_file)
            
            # Read Excel with Polars - handle different sheet specifications
            try:
                # Try with sheet_id parameter first
                if isinstance(sheet_name, int):
                    df = pl.read_excel(uploaded_file, sheet_id=sheet_name)
                else:
                    df = pl.read_excel(uploaded_file, sheet_name=sheet_name)
            except Exception:
                # Fallback: read the first sheet if sheet specification fails
                try:
                    df = pl.read_excel(uploaded_file, sheet_id=0)
                except Exception:
                    # Final fallback: try without sheet specification
                    result = pl.read_excel(uploaded_file)
                    # If result is a dict (multiple sheets), take the first one
                    if isinstance(result, dict):
                        df = list(result.values())[0]
                    else:
                        df = result
            
            # Ensure df is a DataFrame
            if isinstance(df, dict):
                df = list(df.values())[0]
            
            info = {
                'file_type': 'Excel',
                'file_size_mb': file_size_mb,
                'rows': len(df),
                'columns': len(df.columns),
                'memory_mb': df.estimated_size() / (1024 * 1024)
            }
            
            return df, info
            
        except Exception as e:
            raise Exception(f"Error reading Excel file: {str(e)}")

    def load_json_file(uploaded_file) -> tuple[pl.DataFrame, dict]:
        """Load JSON file with error handling"""
        try:
            # Get file info
            file_size_mb = get_file_size_mb(uploaded_file)
            
            # Read JSON with Polars
            # Polars read_json expects NDJSON format, so we might need special handling
            try:
                df = pl.read_json(uploaded_file)
            except Exception:
                # Fallback: try reading as regular JSON with pandas then convert
                import pandas as pd
                pd_df = pd.read_json(uploaded_file)
                df = pl.from_pandas(pd_df)
            
            info = {
                'file_type': 'JSON',
                'file_size_mb': file_size_mb,
                'rows': len(df),
                'columns': len(df.columns),
                'memory_mb': df.estimated_size() / (1024 * 1024)
            }
            
            return df, info
            
        except Exception as e:
            raise Exception(f"Error reading JSON file: {str(e)}")

    def load_parquet_file(uploaded_file) -> tuple[pl.DataFrame, dict]:
        """Load Parquet file with error handling"""
        try:
            # Get file info
            file_size_mb = get_file_size_mb(uploaded_file)
            
            # Read Parquet with Polars
            df = pl.read_parquet(uploaded_file)
            
            info = {
                'file_type': 'Parquet',
                'file_size_mb': file_size_mb,
                'rows': len(df),
                'columns': len(df.columns),
                'memory_mb': df.estimated_size() / (1024 * 1024)
            }
            
            return df, info
            
        except Exception as e:
            raise Exception(f"Error reading Parquet file: {str(e)}")

    def preview_dataframe(df: pl.DataFrame, title: str = "Data Preview"):
        """Show DataFrame preview with statistics"""
        st.subheader(title)
        
        # Basic statistics
        col1, col2= st.columns(2)
        with col1:
            st.metric("Rows", f"{len(df):,}")
        with col2:
            st.metric("Columns", len(df.columns))
        
        preview_df = df.head(100).to_pandas()
        st.dataframe(preview_df, use_container_width=True)
        

    # Main UI
    st.title("📤 Upload Data")

    # Ensure file_uploader_key exists in session state
    if 'file_uploader_key' not in st.session_state:
        st.session_state['file_uploader_key'] = 0

    # File uploader with a dynamic key
    uploaded_file = st.file_uploader(
        "Choose a file",
        type=['csv', 'xlsx', 'xls', 'json', 'parquet'],
        help="Supported formats: CSV, Excel, JSON, Parquet",
        key=f"file_uploader_key_{st.session_state['file_uploader_key']}"
    )

    if uploaded_file is not None:
        # Get file details
        file_name = uploaded_file.name
        file_extension = Path(file_name).suffix.lower()
        file_size_mb = get_file_size_mb(uploaded_file)
        
        # Show file size warning for large files
        if file_size_mb > 100:
            st.warning(f"⚠️ Large file detected ({file_size_mb:.1f} MB). Loading may take some time.")
        elif file_size_mb > 500:
            st.error(f"🚫 File too large ({file_size_mb:.1f} MB). Consider using a smaller sample or streaming approach.")
            st.stop()
        # File-specific options
        load_options = {}
        
        if file_extension == '.csv':
            with st.expander("CSV Options"):
                col1, col2, col3 = st.columns(3)        
                with col1:
                    encoding = st.selectbox("Encoding", ["utf-8", "latin-1", "cp1252"], index=0)
                    load_options['encoding'] = encoding
                with col2:
                    separator = st.selectbox("Separator", [",", ";", "\t", "|"], index=0)
                    load_options['sep'] = separator
                with col3:
                    has_header = st.checkbox("Has header row", value=True)
                    load_options['header'] = 0 if has_header else None
                # Robust parsing is enabled by default for all CSV files
                load_options['robust_parsing'] = True
                st.info("🛡️ Robust parsing is enabled by default to handle encoding issues, malformed rows, and mixed data types automatically.")
        
        elif file_extension in ['.xlsx', '.xls']:
            st.subheader("Excel Options")
            
            # Create a unique key for caching based on file name and size
            file_cache_key = f"excel_sheets_{file_name}_{file_size_mb:.2f}"
            
            # Get Excel sheet information with caching
            if file_cache_key not in st.session_state:
                try:
                    with st.spinner("Analyzing Excel file structure..."):
                        sheets_info = get_excel_sheets_info(uploaded_file)
                        st.session_state[file_cache_key] = sheets_info
                except Exception as e:
                    st.warning(f"Could not analyze Excel structure: {str(e)}. Using default settings.")
                    sheets_info = {'Sheet1': {'name': 'Sheet1', 'estimated_rows': 'Unknown', 'columns': 'Unknown', 'tables': []}}
                    st.session_state[file_cache_key] = sheets_info
            else:
                sheets_info = st.session_state[file_cache_key]
            
            # Sheet selection
            sheet_names = list(sheets_info.keys())
            if len(sheet_names) > 1:
                col1, col2 = st.columns(2)
                with col1:
                    selected_sheet = st.selectbox(
                        "Select Sheet", 
                        sheet_names,
                        index=0,
                        help="Choose which sheet to load from the Excel file"
                    )
                
                with col2:
                    # Show sheet info
                    sheet_info = sheets_info[selected_sheet]
                    st.info(f"📊 **Sheet Info:**\n- Rows: ~{sheet_info['estimated_rows']}\n- Columns: ~{sheet_info['columns']}")
            else:
                selected_sheet = sheet_names[0]
                st.info(f"📊 **Sheet:** {selected_sheet}")
            
            load_options['sheet_name'] = selected_sheet
            
            # Table selection (if available)
            selected_sheet_info = sheets_info[selected_sheet]
            if selected_sheet_info['tables']:
                st.subheader("📋 Tables Found")
                
                # Add option to load entire sheet or specific table
                load_option = st.radio(
                    "What would you like to load?",
                    ["Entire Sheet", "Specific Table"],
                    index=0,
                    help="Choose to load the entire sheet or a specific table within the sheet"
                )
                
                if load_option == "Specific Table":
                    table_options = [f"{table['display_name']} ({table['name']})" for table in selected_sheet_info['tables']]
                    selected_table_display = st.selectbox(
                        "Select Table",
                        table_options,
                        help="Choose which table to load from the selected sheet"
                    )
                    
                    # Extract the actual table name
                    selected_table = None
                    for table in selected_sheet_info['tables']:
                        if f"{table['display_name']} ({table['name']})" == selected_table_display:
                            selected_table = table['name']
                            break
                    
                    load_options['table_name'] = selected_table
                    st.info(f"🎯 **Selected Table:** {selected_table}")
                else:
                    load_options['table_name'] = None
            else:
                load_options['table_name'] = None
                if selected_sheet_info['tables'] == []:  # Empty list, not None
                    st.info("ℹ️ No tables detected in this sheet. The entire sheet will be loaded.")
            
            # Add option to specify header row for Excel
            has_header = st.checkbox("Has header row", value=True, help="Check if the first row contains column names")
            load_options['has_header'] = has_header

        # Default dataset name from filename
        default_name = Path(file_name).stem
        dataset_name = st.text_input(
            "Choose Dataset Name",
            value=default_name,
            help="Enter a unique name for this dataset"
        )
        
        # Validate dataset name
        name_valid, name_message = validate_dataset_name(dataset_name)
        if not name_valid:
            st.error(f"❌ {name_message}")
        
        st.subheader(f"**Check if the preview looks okay, then you can save the dataset:**")
        col1, col2, col = st.columns([1, 1, 3])
        with col1:
            # Load and preview button with explicit styling
            if st.button("🔍 Load and Preview", disabled=not name_valid, type="secondary"):
                try:
                    with st.spinner(f"Loading {file_name}..."):
                        start_time = time.time()
                        # Load file based on extension
                        if file_extension == '.csv':
                            df, file_info = load_csv_file(uploaded_file, **load_options)
                        elif file_extension in ['.xlsx', '.xls']:
                            # Check if user wants to load a specific table
                            if load_options.get('table_name'):
                                df, file_info = load_excel_sheet_or_table(
                                    uploaded_file, 
                                    sheet_name=load_options['sheet_name'],
                                    table_name=load_options['table_name']
                                )
                            else:
                                df, file_info = load_excel_file(uploaded_file, sheet_name=load_options['sheet_name'])
                        elif file_extension == '.json':
                            df, file_info = load_json_file(uploaded_file)
                        elif file_extension == '.parquet':
                            df, file_info = load_parquet_file(uploaded_file)
                        else:
                            st.error(f"Unsupported file type: {file_extension}")
                            st.stop()
                        
                        load_time = time.time() - start_time
                        
                        # Store in session state for preview
                        st.session_state.preview_df = df
                        st.session_state.preview_info = file_info
                        st.session_state.preview_dataset_name = dataset_name
                        st.session_state.preview_filename = file_name
                        
                except Exception as e:
                    st.error(f"❌ Error loading file: {str(e)}")
                    log_message(f"File loading error: {str(e)}", level="error")
                    log_message(traceback.format_exc(), level="error")

        with col2:
            save_button = st.button("💾 Save Dataset", type="primary", disabled=not name_valid)
            
            if save_button and name_valid:
                try:
                    with st.spinner("Loading and saving dataset..."):
                        start_time = time.time()
                        
                        # Check if we have preview data, if not load the file
                        if (hasattr(st.session_state, 'preview_df') and 
                            st.session_state.preview_df is not None and
                            hasattr(st.session_state, 'preview_dataset_name')):
                            # Use existing preview data
                            df_to_save = st.session_state.preview_df
                            dataset_name_to_save = st.session_state.preview_dataset_name
                            filename_to_save = st.session_state.preview_filename
                        else:
                            # Load the file fresh for saving
                            if file_extension == '.csv':
                                df_to_save, file_info = load_csv_file(uploaded_file, **load_options)
                            elif file_extension in ['.xlsx', '.xls']:
                                if load_options.get('table_name'):
                                    df_to_save, file_info = load_excel_sheet_or_table(
                                        uploaded_file, 
                                        sheet_name=load_options['sheet_name'],
                                        table_name=load_options['table_name']
                                    )
                                else:
                                    df_to_save, file_info = load_excel_file(uploaded_file, sheet_name=load_options['sheet_name'])
                            elif file_extension == '.json':
                                df_to_save, file_info = load_json_file(uploaded_file)
                            elif file_extension == '.parquet':
                                df_to_save, file_info = load_parquet_file(uploaded_file)
                            else:
                                st.error(f"Unsupported file type: {file_extension}")
                                st.stop()
                            
                            dataset_name_to_save = dataset_name
                            filename_to_save = file_name
                        
                        # Store dataset using the file storage system
                        if uploaded_file is not None:
                            # Get the file size from the uploaded file
                            file_size_bytes = uploaded_file.size if hasattr(uploaded_file, 'size') else len(uploaded_file.getvalue())
                            
                            # Store the dataset with correct size information
                            dataset_id = store_dataset(
                                dataset_name=dataset_name_to_save,
                                df=df_to_save,
                                original_filename=filename_to_save,
                                file_size_bytes=file_size_bytes
                            )
                        
                        save_time = time.time() - start_time
                        st.success(f"✅ Dataset '{dataset_name_to_save}' saved successfully in {save_time:.2f}s!")
                        
                        # Clear preview data
                        if 'preview_df' in st.session_state:
                            del st.session_state.preview_df
                        if 'preview_info' in st.session_state:
                            del st.session_state.preview_info
                        if 'preview_dataset_name' in st.session_state:
                            del st.session_state.preview_dataset_name
                        if 'preview_filename' in st.session_state:
                            del st.session_state.preview_filename
                        
                        # Increment the file uploader key to reset the widget
                        st.session_state['file_uploader_key'] += 1
                        st.rerun()
                        
                except Exception as e:
                    st.error(f"❌ Error saving dataset: {str(e)}")
                    log_message(f"Dataset saving error: {str(e)}", level="error")
                    log_message(traceback.format_exc(), level="error")


    # Preview and save section
    if hasattr(st.session_state, 'preview_df') and st.session_state.preview_df is not None:
        st.markdown("---")
        
        # Show preview
        preview_dataframe(st.session_state.preview_df, "Preview of Loaded Data")

        
    # Show existing datasets
    if st.session_state.lazy_datasets:
        st.markdown("---")
        st.header("📚 Existing Datasets")
        
        # Display datasets in a more interactive format
        for name, lazy_dataset in st.session_state.lazy_datasets.items():
            with st.container():
                col1, col2, col3, col4 = st.columns([3, 1, 1, 1])
                
                with col1:
                    st.markdown(f"### {name}")
                    
                    # Calculate consistent size display
                    memory_mb = lazy_dataset.memory_usage_mb
                    file_size_bytes = lazy_dataset.metadata.get('file_size_bytes', 0)
                    
                    # Use file size if available, otherwise use memory usage
                    if file_size_bytes > 0:
                        display_size = file_size_bytes / (1024 * 1024)  # Convert to MB
                        size_label = "File Size"
                    else:
                        display_size = memory_mb
                        size_label = "Memory"
                    
                    st.markdown(f"**Rows:** {lazy_dataset.shape[0]:,} | **Columns:** {lazy_dataset.shape[1]} | **{size_label}:** {display_size:.1f} MB")
                    

                with col2:
                    if st.button(f"👁️ Preview", key=f"preview_{name}", help="Quick preview of the dataset"):
                        st.session_state.show_preview = name
                
                with col3:
                    if st.button(f"📊 Explore", key=f"explore_{name}", help="Open in Data Explorer"):
                        st.session_state.active_dataset = name
                        st.switch_page("pages/2_Data_Explorer.py")
                
                with col4:
                    if st.button(f"🗑️ Delete", key=f"delete_{name}", help="Delete this dataset", type="secondary"):
                        st.session_state.confirm_delete = name
            
            # Show preview if requested
            if hasattr(st.session_state, 'show_preview') and st.session_state.show_preview == name:
                with st.expander(f"Preview of {name}", expanded=True):
                    try:
                        preview_df = lazy_dataset.get_sample_cached(20)
                        
                        # Show basic info with corrected size calculation
                        col1, col2, col3, col4 = st.columns(4)
                        with col1:
                            st.metric("Sample Rows", len(preview_df))
                        with col2:
                            st.metric("Total Rows", f"{lazy_dataset.shape[0]:,}")
                        with col3:
                            st.metric("Total Columns", lazy_dataset.shape[1])
                        with col4:
                            # Calculate preview size accurately
                            preview_memory = preview_df.memory_usage(deep=True).sum() / 1024 / 1024
                            st.metric("Preview Size", f"{preview_memory:.1f} MB")
                        
                        # Show sample data
                        st.dataframe(preview_df, use_container_width=True)
                            
                    except Exception as e:
                        st.error(f"Error loading preview: {str(e)}")
            
            # Add visual separator between datasets
            st.markdown('<hr style="margin: 0.7rem 0; border: none; height: 1px; background: linear-gradient(to right, #e0e0e0, #f0f0f0, #e0e0e0);">', unsafe_allow_html=True)

        
        # Handle delete confirmation
        if hasattr(st.session_state, 'confirm_delete'):
            dataset_to_delete = st.session_state.confirm_delete
            
            st.warning(f"⚠️ Are you sure you want to delete dataset '{dataset_to_delete}'?")
            col1, col2, col3 = st.columns([1, 1, 2])
            
            with col1:
                if st.button("✅ Yes, Delete", type="primary"):
                    try:
                        from app_core.config import remove_dataset
                        remove_dataset(dataset_to_delete)
                        st.success(f"Dataset '{dataset_to_delete}' deleted successfully!")
                        
                        # Clear the confirmation state
                        del st.session_state.confirm_delete
                        if hasattr(st.session_state, 'show_preview'):
                            del st.session_state.show_preview
                        
                        st.rerun()
                    except Exception as e:
                        st.error(f"Error deleting dataset: {str(e)}")
            
            with col2:
                if st.button("❌ Cancel"):
                    del st.session_state.confirm_delete
                    st.rerun()