"""
Utility module for handling CSS styling in the application.
This centralizes all styling to improve performance and maintainability.
"""

import os
import streamlit as st

# Define the app's theme colors with consistent blue theme
THEME_COLORS = {
    'primary': '#4e8cff',           # Primary blue
    'secondary': '#4e8cff',         # Secondary blue (same as primary for consistency)
    'secondary_hover': '#3a78f0',   # Darker blue for hover states
    'background': '#FFFFFF',        # Background white
    'text': '#31333F',              # Main text color
    'accent': '#4e8cff',            # Accent color (same as primary)
    'success': '#09AB3B',           # Success green
    'warning': '#F9AC2F',           # Warning yellow/orange
    'error': '#FF4B4B',             # Error red
    'gray_light': '#F0F2F6',        # Light gray for backgrounds
    'gray_medium': '#D1D5DB',       # Medium gray for borders
    'gray_dark': '#737A8A'          # Dark gray for secondary text
}

# Cache the CSS loading to avoid repeated file reads
# @st.cache_data  # Temporarily disabled for development/debugging
def load_css():
    """
    Load the CSS from the static/styles.css file.
    Uses caching to avoid repeated file reads.
    """
    css_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "static", "styles.css")
    
    try:
        with open(css_path, "r") as f:
            return f.read()
    except Exception as e:
        print(f"Error loading CSS: {str(e)}")
        return ""

def apply_css():
    """
    Apply the CSS to the current Streamlit page.
    This should be called once at the beginning of each page.
    """
    css = load_css()
    if css:
        st.markdown(f"<style>{css}</style>", unsafe_allow_html=True)    # Apply dynamic CSS with theme colors
    st.markdown(f"""
    <style>
    /* Theme color variables */
    :root {{
        --primary-color: {THEME_COLORS['primary']};
        --secondary-color: {THEME_COLORS['secondary']};
        --secondary-hover-color: {THEME_COLORS['secondary_hover']};
        --background-color: {THEME_COLORS['background']};
        --text-color: {THEME_COLORS['text']};
        --accent-color: {THEME_COLORS['accent']};
        --success-color: {THEME_COLORS['success']};
        --warning-color: {THEME_COLORS['warning']};
        --error-color: {THEME_COLORS['error']};
        --gray-light: {THEME_COLORS['gray_light']};
        --gray-medium: {THEME_COLORS['gray_medium']};
        --gray-dark: {THEME_COLORS['gray_dark']};
    }}
    
    /* Reduce top whitespace across all pages */
    .main .block-container {{
        padding-top: 1rem !important;
        margin-top: 0 !important;
    }}
    
    section[data-testid="stMain"] {{
        padding-top: 0.5rem !important;
    }}
    
    section.main > div.block-container {{
        padding-top: 1rem !important;
        margin-top: 0 !important;
    }}
    
    /* Apply theme colors to various elements */
    .result-section-header {{
        background-color: var(--secondary-color);
    }}
    
    .streamlit-expanderHeader {{
        border-left: 5px solid var(--secondary-color);
    }}
    
    .stTabs [aria-selected="true"] {{
        border-bottom-color: var(--secondary-color);
    }}
      div[data-testid="stButton"] button {{
        background-color: var(--secondary-color) !important;
        color: white !important;
        font-weight: 500 !important;
        border: none !important;
        border-radius: 5px !important;
        transition: all 0.3s ease !important;
    }}
    
    div[data-testid="stButton"] button:hover {{
        background-color: color-mix(in srgb, var(--secondary-color) 85%, black) !important;
        color: white !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
    }}
    
    div[data-testid="stButton"] button:active {{
        color: white !important;
        transform: translateY(0px) !important;
    }}
    
    div[data-testid="stButton"] button:focus {{
        color: white !important;
        outline: none !important;
        box-shadow: 0 0 0 2px rgba(0, 104, 201, 0.3) !important;
    }}
    
    /* Disabled button styling */
    div[data-testid="stButton"] button:disabled {{
        background-color: var(--gray-medium) !important;
        color: var(--gray-dark) !important;
        cursor: not-allowed !important;
        transform: none !important;
        box-shadow: none !important;
    }}
    
    /* Primary button styling */
    div[data-testid="stButton"] button[kind="primary"] {{
        background-color: var(--primary-color) !important;
        color: white !important;
    }}
    
    div[data-testid="stButton"] button[kind="primary"]:hover {{
        background-color: color-mix(in srgb, var(--primary-color) 85%, black) !important;
        color: white !important;
    }}
    
    /* Secondary button styling */
    div[data-testid="stButton"] button[kind="secondary"] {{
        background-color: var(--gray-light) !important;
        color: var(--text-color) !important;
        border: 1px solid var(--gray-medium) !important;
    }}
    
    div[data-testid="stButton"] button[kind="secondary"]:hover {{
        background-color: var(--gray-medium) !important;
        color: var(--text-color) !important;
    }}
    
    /* Sidebar button styling */
    .stSidebar div[data-testid="stButton"] button {{
        background-color: var(--secondary-color) !important;
        color: white !important;
    }}
    
    .stSidebar div[data-testid="stButton"] button:hover {{
        background-color: color-mix(in srgb, var(--secondary-color) 85%, black) !important;
        color: white !important;
    }}
    
    /* Form submit button styling */
    div[data-testid="stForm"] div[data-testid="stButton"] button {{
        background-color: var(--secondary-color) !important;
        color: white !important;
    }}
    
    div[data-testid="stForm"] div[data-testid="stButton"] button:hover {{
        background-color: color-mix(in srgb, var(--secondary-color) 85%, black) !important;
        color: white !important;
    }}
    
    /* Consistent styling for dataset view buttons */
    div[data-testid="stButton"] button.view-dataset-btn,
    div[data-testid="stButton"] button.dataset-view-btn {{
        background-color: var(--secondary-color) !important;
        color: white !important;
        font-weight: 500 !important;
        border-radius: 5px;
        border: none !important;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;
        transition: all 0.3s ease;
        width: 100%;
    }}
    
    div[data-testid="stButton"] button.view-dataset-btn:hover,
    div[data-testid="stButton"] button.dataset-view-btn:hover {{
        background-color: color-mix(in srgb, var(--secondary-color) 85%, black) !important;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
    }}
    
    .floating-scroll-button {{
        background-color: var(--secondary-color);
    }}
    
    .floating-scroll-button:hover {{
        background-color: color-mix(in srgb, var(--secondary-color) 85%, black);
    }}
    
    /* Additional theme color applications can be added here */
    </style>
    """, unsafe_allow_html=True)

def ensure_button_text_visibility():
    """
    Helper function to ensure button text visibility across all button states.
    This function applies comprehensive button styling that fixes text color issues.
    
    This is already included in apply_css(), but can be called separately if needed.
    """
    st.markdown("""
    <style>
    /* Emergency button text fix - use only if apply_css() is not working */
    div[data-testid="stButton"] button {
        color: white !important;
    }
    div[data-testid="stButton"] button:hover {
        color: white !important;
    }
    div[data-testid="stButton"] button:active {
        color: white !important;
    }
    div[data-testid="stButton"] button:focus {
        color: white !important;
    }
    </style>
    """, unsafe_allow_html=True)

def get_button_styling_info():
    """
    Returns information about the current button styling system.
    Useful for debugging button text color issues.
    """
    return {
        "description": "Comprehensive button styling system",
        "features": [
            "Universal white text on all button states",
            "Consistent background colors using theme variables",
            "Proper hover, focus, and active state handling", 
            "Disabled button styling",
            "Primary, secondary, and default button variants",
            "Sidebar button compatibility",
            "Form button compatibility"
        ],
        "files_involved": [
            "app_core/utils/styling.py - Main styling functions",
            "static/styles.css - Static CSS rules"
        ],
        "usage": "Simply call apply_css() at the beginning of each page",
        "troubleshooting": {
            "problem": "Button text still not visible",
            "solutions": [
                "Ensure apply_css() is called after st.set_page_config()",
                "Remove any conflicting inline CSS from the page",
                "Call ensure_button_text_visibility() as a fallback",
                "Check for theme conflicts or custom CSS overrides"
            ]
        }
    }

def get_color(color_name):
    """
    Get a color from the theme.
    
    Args:
        color_name: The name of the color to get
        
    Returns:
        The color value
    """
    return THEME_COLORS.get(color_name, THEME_COLORS['primary'])
