# Application Pages

The Pandas-AI application is divided into several pages, each serving a specific function. The pages are located in the `pages/` directory and are automatically detected by Streamlit to build the navigation sidebar.

## Welcome Page (`Welcome.py`)

This is the main entry point of the application. It serves as a landing page, providing a brief introduction to the application and guiding users to the other pages.

## 1. Upload Data (`pages/1_Upload_Data.py`)

This page allows users to upload their datasets into the application. It supports various file formats, such as CSV and Excel. Once a file is uploaded, it is stored in the `dataset_storage/` directory, and its metadata is saved for future use.

## 2. Data Explorer (`pages/2_Data_Explorer.py`)

The Data Explorer is a multi-functional page where users can perform in-depth analysis of their datasets. It is composed of several modules, which are located in the `pages/explorer_modules/` directory. These modules include:
-   **Data Profiling**: Generates a detailed report of the dataset, including statistics, distributions, and missing values.
-   **Data Quality**: Assesses the quality of the data, identifying potential issues like duplicates or inconsistencies.
-   **Visual Explorer**: An interactive interface for creating various types of charts and visualizations from the data.
-   **Dataset Comparison**: Allows users to compare two different datasets.

## 3. Chat (`pages/3_Chat.py`)

This page features an AI-powered chat interface. Users can ask questions about their loaded datasets in natural language, and the AI will provide answers, generate insights, and even create visualizations based on the user's queries.

## 5. Database Connections (`pages/5_Database_Connections.py`)

This page provides a user interface for managing connections to Oracle databases. Users can add, edit, and save connection details, which are then used by the application to fetch data from the specified databases.

## 7. View Logs (`pages/7_View_Logs.py`)

This page allows users to view the application's log files. This is useful for debugging and monitoring the application's activity.

## 8. Logout (`pages/8_Logout.py`)

This page provides a way for users to log out of the application, clearing their session.

---

Next, learn about the [Explorer Modules](explorer_modules.md).
