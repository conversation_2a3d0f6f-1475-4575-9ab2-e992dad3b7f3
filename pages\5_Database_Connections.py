# Database Connections Page
# Allows users to connect to Oracle databases, explore content, and import datasets

import streamlit as st
import os
import sys
from app_core.utils.logging import log_message
import time
import re
import traceback
from typing import Optional, Dict, List, Any
from dotenv import load_dotenv

# Page configuration
st.set_page_config(
    page_title="Database Connections",
    page_icon="🗄️",
    layout="wide",
    initial_sidebar_state="expanded"
)

if False:
# if not st.user.is_logged_in:
    st.login("microsoft")
else:
    # Load environment variables
    log_message("Environment variables loaded for Database Connections page.")
    load_dotenv()

    # Add the parent directory to the path to import modules
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

    # Import required modules
    from app_core.database.oracle_manager import OracleManager
    from app_core.config import init_session_state, store_dataset
    from app_core.config import get_file_storage


    # Initialize session state
    init_session_state()
    log_message("Session state initialized for Database Connections page.")

    # Custom CSS for better styling and to fix header covering the title
    st.markdown("""
    <style>
    .connection-card {
        padding: 20px;
        border-radius: 10px;
        border: 2px solid #e0e0e0;
        margin: 10px 0;
        background-color: #f8f9fa;
    }

    .connection-card.connected {
        border-color: #28a745;
        background-color: #d4edda;
    }

    .table-card {
        padding: 15px;
        border-radius: 8px;
        border: 1px solid #dee2e6;
        margin: 8px 0;
        background-color: #ffffff;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* Fix: prevent header from covering the title */
    [data-testid="stHeader"] {
        z-index: 0 !important;
        position: relative !important;
        background: transparent !important;
        box-shadow: none !important;
        height: 0 !important;
        min-height: 0 !important;
        padding: 0 !important;
        margin: 0 !important;
    }

    /* Remove white space above the title */
    [data-testid="stAppViewContainer"] > .main,
    [data-testid="stMainBlock"],
    [data-testid="stMainBlockContainer"],
    .block-container {
        padding-top: 0 !important;
        margin-top: 0 !important;
    }
    .main .block-container > :first-child {
        margin-top: 0 !important;
        padding-top: 0 !important;
    }
    </style>
    """, unsafe_allow_html=True)

    # Initialize session state for this page
    def initialize_db_session_state():
        """Initialize database-specific session state variables."""
        if 'oracle_connections' not in st.session_state:
            st.session_state.oracle_connections = {}
        if 'selected_connection' not in st.session_state:
            st.session_state.selected_connection = None

    # Sidebar: Show current datasets and connection status
    def render_sidebar():
        """Render the sidebar with dataset info and connection status."""
        with st.sidebar:
            # Render user authentication info

            # --- Custom: Show ROLE, COMPANY, USER_TYPE from APEXADD_EPRDATA in DWH_PUBLIC ---
            st.header("🔎 User Info")
            # st.write(f"**Mail:** {st.user.email}")
            try:
                # Find a connected OracleManager
                oracle_manager = None
                for conn in st.session_state.get('oracle_connections', {}).values():
                    if conn.get('status') == 'connected':
                        oracle_manager = conn.get('manager')
                        break
                # if oracle_manager:
                #     query = "SELECT ROLE, COMPANY, USER_TYPE FROM DWH_PUBLIC.APEXADD_EPRDATA WHERE USERNAME = :username"
                #     result = oracle_manager.execute_query(query, params={'username': st.user.email.upper()}, limit=10)
                #     print(f"Query executed: {query} with params: {st.user.email.upper()}")
                #     if result is not None and not result.is_empty():
                #         st.write(f"**Role:** {result['ROLE'][0]}")
                #         st.write(f"**Company:** {result['COMPANY'][0]}")
                #         st.write(f"**User Type:** {result['USER_TYPE'][0]}")
                #     else:
                #         st.info("No data found in DWH_PUBLIC.APEXADD_EPRDATA.")
                # else:
                #     st.info("Connect to a database to view user info.")
            except Exception as e:
                st.warning(f"Could not retrieve user info: {e}")

            st.header("📚 Current Datasets")
            if st.session_state.lazy_datasets:
                for name, lazy_dataset in st.session_state.lazy_datasets.items():
                    st.markdown(f"**{name}**")
                    st.caption(f"{lazy_dataset.shape[0]:,} rows × {lazy_dataset.shape[1]} cols")
            else:
                st.info("No datasets loaded yet.")
            
            # Connection status
            st.header("🔗 Connections")
            if st.session_state.oracle_connections:
                for conn_name, conn_info in st.session_state.oracle_connections.items():
                    status_icon = "🟢" if conn_info['status'] == 'connected' else "🔴"
                    st.markdown(f"{status_icon} **{conn_name}**")
            else:
                st.info("No active connections.")

    # --- Ensure session state is initialized before any connection logic ---
    def ensure_db_session_state():
        if 'oracle_connections' not in st.session_state:
            st.session_state.oracle_connections = {}
        if 'selected_connection' not in st.session_state:
            st.session_state.selected_connection = None

    # --- Move perform_quick_connect definition above auto_quick_connect and render_quick_connect ---
    def perform_quick_connect(connection_type, oci_wallet_ready, traditional_ready):
        """Perform quick connection using environment variables."""
        try:
            connection_name = f"ENV_{connection_type}_{int(time.time())}"
            if oci_wallet_ready:
                connection_params = {
                    'username': os.getenv("OCI_USERNAME"),
                    'password': os.getenv("OCI_PASSWORD"),
                    'dsn': os.getenv("OCI_DNS_NAME"),
                    'wallet_password': os.getenv("OCI_WALLET_PASSWORD")
                }
            else:
                connection_params = {
                    'host': os.getenv("OCI_HOST"),
                    'port': int(os.getenv("ORACLE_DB_PORT", "1521")),
                    'username': os.getenv("OCI_USERNAME"),
                    'password': os.getenv("OCI_PASSWORD"),
                    'service_name': os.getenv("OCI_SERVICE_NAME"),
                    'encoding': os.getenv("ORACLE_ENCODING", "UTF-8"),
                    'connection_timeout': int(os.getenv("ORACLE_CONNECTION_TIMEOUT", "30"))
                }
            with st.spinner(f"Connecting using {connection_type} method..."):
                oracle_manager = OracleManager()
                if oracle_manager.create_connection(connection_name=connection_name, **connection_params):
                    st.session_state.oracle_connections[connection_name] = {
                        'manager': oracle_manager,
                        'params': connection_params,
                        'status': 'connected',
                        'type': connection_type
                    }
                    st.session_state.selected_connection = connection_name
                    st.success(f"✅ Successfully connected via {connection_type}!")
                    st.rerun()
                else:
                    st.error("❌ Connection failed. Please check your credentials.")
        except Exception as e:
            st.error(f"❌ Connection error: {str(e)}")
            log_message(f"Quick connect failed: {e}", level="error")

    # Automatic Quick Connect with Environment Variables
    def auto_quick_connect():
        """Automatically connect using environment variables if not already connected."""
        ensure_db_session_state()
        # Check if already connected
        if st.session_state.get('oracle_connections'):
            for conn in st.session_state['oracle_connections'].values():
                if conn.get('status') == 'connected':
                    return  # Already connected
        # Check environment variables
        username = os.getenv("OCI_USERNAME")
        password = os.getenv("OCI_PASSWORD")
        dsn = os.getenv("OCI_DNS_NAME")
        wallet_password = os.getenv("OCI_WALLET_PASSWORD")
        host = os.getenv("OCI_HOST")
        service_name = os.getenv("OCI_SERVICE_NAME")
        oci_wallet_ready = all([username, password, dsn, wallet_password])
        traditional_ready = all([host, username, password, service_name])
        if oci_wallet_ready:
            connection_type = "OCI Wallet"
        elif traditional_ready:
            connection_type = "Traditional"
        else:
            return  # Not enough info to connect
        # Perform connection
        perform_quick_connect(connection_type, oci_wallet_ready, traditional_ready)

    # Call auto_quick_connect at app start
    auto_quick_connect()

    # Manual Connection Form
    def render_connection_form():
        """Render manual connection form."""
        st.subheader("🔧 Manual Connection")
        
        with st.form("manual_connection_form"):
            col1, col2 = st.columns(2)
            
            with col1:
                connection_name = st.text_input("Connection Name *", placeholder="e.g., Production DB")
                username = st.text_input("Username *", value=os.getenv("OCI_USERNAME", ""))
                password = st.text_input("Password *", value=os.getenv("OCI_PASSWORD", ""), type="password")
            
            with col2:
                host = st.text_input("Host *", value=os.getenv("OCI_HOST", ""))
                port = st.number_input("Port *", min_value=1, max_value=65535, value=1521)
                service_name = st.text_input("Service Name *", value=os.getenv("OCI_SERVICE_NAME", ""))
            
            submitted = st.form_submit_button("🔌 Connect to Database", type="primary")
        
        if submitted:
            perform_manual_connection(connection_name, username, password, host, port, service_name)

    def perform_manual_connection(connection_name, username, password, host, port, service_name):
        """Perform manual connection with provided parameters."""
        try:
            if not all([connection_name, host, username, password, service_name]):
                st.error("❌ Please fill in all required fields marked with *")
                return

            connection_params = {
                'host': host,
                'port': port,
                'username': username,
                'password': password,
                'service_name': service_name,
                'encoding': 'UTF-8',
                'connection_timeout': 30
            }

            # Debug: Log connection parameters (excluding password)
            safe_params = connection_params.copy()
            safe_params['password'] = '***'
            log_message(f"Attempting manual connection with params: {safe_params}", level="info")
            st.write(f"Debug: Connecting with params: {safe_params}")

            with st.spinner(f"Connecting to {connection_name}..."):
                oracle_manager = OracleManager()

                result = oracle_manager.create_connection(connection_name=connection_name, **connection_params)
                log_message(f"create_connection result: {result}", level="info")
                if result:
                    st.session_state.oracle_connections[connection_name] = {
                        'manager': oracle_manager,
                        'params': connection_params,
                        'status': 'connected',
                        'type': 'Traditional'
                    }
                    st.session_state.selected_connection = connection_name
                    st.success(f"✅ Successfully connected to {connection_name}!")
                    st.rerun()
                else:
                    st.error("❌ Connection failed. Please verify your credentials and connection details.")
                    log_message(f"Manual connection failed: create_connection returned False. Params: {safe_params}", level="error")

        except Exception as e:
            st.error(f"❌ Connection error: {str(e)}")
            log_message(f"Manual connection failed: {e}\n{traceback.format_exc()}", level="error")

    # Database Explorer
    def render_database_explorer():
        """Render database exploration section."""
        if not st.session_state.selected_connection:
            st.info("👆 Please connect to a database first to explore its contents.")
            return
        
        conn_name = st.session_state.selected_connection
        conn_info = st.session_state.oracle_connections[conn_name]
        oracle_manager = conn_info['manager']
        
        st.subheader(f"📊 Explore Database - {conn_name}")
        
        try:       
            # Load schemas
            with st.spinner("Loading schemas..."):
                schemas = oracle_manager.get_schemas()
            
            if not schemas:
                st.warning("No schemas found or insufficient privileges.")
                return
            
            # By default, allow all schemas
            allowed_schemas = schemas
            # If you want to keep the permission logic, comment out the above and uncomment below:
            # allowed_schemas = auth.get_allowed_schemas(schemas)
            
            if not allowed_schemas:
                st.error("🚫 No schemas available.")
                return
            
            # Show schema access info if filtering is re-enabled
            # if len(allowed_schemas) < len(schemas):
            #     st.info(f"📋 Showing {len(allowed_schemas)} of {len(schemas)} schemas (based on your permissions)")
            
            # Schema selection
            selected_schema = st.selectbox("Select Schema:", options=allowed_schemas)
            
            if selected_schema:
                # --- Caching tables/views per connection+schema ---
                cache_key = f"tv_result_{conn_name}_{selected_schema}"
                if cache_key not in st.session_state:
                    with st.spinner(f"Loading tables and views from {selected_schema}..."):
                        tv_result = oracle_manager.get_tables_and_views_with_creation_date(selected_schema)
                        st.session_state[cache_key] = tv_result
                else:
                    tv_result = st.session_state[cache_key]
                tables = tv_result.get('tables', [])
                views = tv_result.get('views', [])


                # --- Use Streamlit tabs for navigation, persist active tab in session_state ---
                tab_labels = ["📋 Tables", "👁️ Views"]
                if 'active_db_tab' not in st.session_state:
                    st.session_state.active_db_tab = 0

                # Streamlit tabs: returns list of tab objects
                tab_objs = st.tabs(tab_labels)

                # Helper to persist tab state after rerun
                def set_active_tab(idx):
                    st.session_state.active_db_tab = idx

                # Tables Tab
                with tab_objs[0]:
                    if st.session_state.active_db_tab != 0:
                        set_active_tab(0)
                    st.markdown("## 📋 Tables")
                    if tables:
                        st.success(f"Found **{len(tables)}** tables in schema `{selected_schema}`")
                        search_term_tables = st.text_input("🔍 Search tables:", placeholder="Enter table name to filter...", key="search_tables")
                        if search_term_tables:
                            filtered_tables = [t for t in tables if search_term_tables.lower() in t['TABLE_NAME'].lower()]
                        else:
                            filtered_tables = tables
                        if filtered_tables:
                            render_table_list(oracle_manager, selected_schema, filtered_tables)
                        else:
                            st.warning(f"No tables found matching '{search_term_tables}'")
                    else:
                        st.warning(f"No tables found in schema `{selected_schema}`.")

                # Views Tab
                with tab_objs[1]:
                    if st.session_state.active_db_tab != 1:
                        set_active_tab(1)
                    st.markdown("## 👁️ Views")
                    if views:
                        st.info(f"Found **{len(views)}** views in schema `{selected_schema}`")
                        search_term_views = st.text_input("🔍 Search views:", placeholder="Enter view name to filter...", key="search_views")
                        if search_term_views:
                            filtered_views = [v for v in views if search_term_views.lower() in v['TABLE_NAME'].lower()]
                        else:
                            filtered_views = views
                        if filtered_views:
                            render_view_list(oracle_manager, selected_schema, filtered_views)
                        else:
                            st.warning(f"No views found matching '{search_term_views}'")
                    else:
                        st.info(f"No views found in schema `{selected_schema}`.")
                    
        except Exception as e:
            st.error(f"Failed to explore database: {e}")
            log_message(f"Database exploration failed: {e}", level="error")

    # Table preview caching
    @st.cache_data(ttl=60, show_spinner=False)
    def get_cached_table_preview(connection_name: str, schema: str, table_name: str, limit: int = 100):
        """Get cached table preview data."""
        try:
            if connection_name in st.session_state.oracle_connections:
                oracle_manager = st.session_state.oracle_connections[connection_name]['manager']
                query = f"SELECT * FROM {schema}.{table_name} WHERE ROWNUM <= {limit}"
                return oracle_manager.execute_query(query, limit=limit)
        except Exception as e:
            log_message(f"Cached preview failed: {e}", level="error")
            return None

    def render_table_list(oracle_manager, schema, tables):
        """Render list of tables with import options."""
        for table in tables:
            table_name = table['TABLE_NAME']
            with st.container():
                st.markdown(f"""
                <div class="table-card">
                    <h4>📋 {table_name}</h4>
                </div>
                """, unsafe_allow_html=True)

                col1, col2, col3 = st.columns([2, 5, 3])

                with col1:
                    # Table information
                    st.markdown("**Table Information:**")
                    st.write(f"• **Rows:** {int(table.get('NUM_ROWS', 0)):,}")
                    st.write(f"• **Columns:** {int(table.get('NUM_COLUMNS', 0)):,}")

                with col2:
                    # Preview toggle
                    preview_key = f"show_preview_{schema}_{table_name}"
                    if st.button(f"👁️ {'Hide' if st.session_state.get(preview_key, False) else 'Show'} Preview", key=f"preview_toggle_{schema}_{table_name}"):
                        st.session_state[preview_key] = not st.session_state.get(preview_key, False)
                        st.rerun()
                    # Show preview if toggled on
                    if st.session_state.get(preview_key, False):
                        show_table_preview(st.session_state.selected_connection, schema, table_name)

                with col3:
                    # Natural language query input for selective import
                    nl_query_key = f"nl_query_{schema}_{table_name}"
                    nl_query = st.text_input(
                        "Natural Language Import Query (optional)",
                        value=st.session_state.get(nl_query_key, ""),
                        key=f"nl_query_input_{schema}_{table_name}"
                    )
                    st.session_state[nl_query_key] = nl_query
                    # Import button
                    if st.button("🚀 Start Import", key=f"import_{schema}_{table_name}", type="primary", use_container_width=True):
                        perform_dataset_import(oracle_manager, schema, table_name, nl_query)
                st.markdown("---")

    # Render list of views (read-only, no import)
    def render_view_list(oracle_manager, schema, views):
        """Render list of views """
        for view in views:
            view_name = view['TABLE_NAME']
            with st.container():
                st.markdown(f"""
                <div class="table-card">
                    <h4>👁️ {view_name}</h4>
                </div>
                """, unsafe_allow_html=True)

                col2, col3 = st.columns([5, 3])

                with col2:
                    # Preview toggle for view
                    preview_key = f"show_preview_{schema}_{view_name}_view"
                    if st.button(f"👁️ {'Hide' if st.session_state.get(preview_key, False) else 'Show'} Preview", key=f"preview_toggle_{schema}_{view_name}_view"):
                        st.session_state[preview_key] = not st.session_state.get(preview_key, False)
                        st.session_state.active_db_tab = 1  # Stay on Views tab
                        st.rerun()
                    if st.session_state.get(preview_key, False):
                        show_table_preview(st.session_state.selected_connection, schema, view_name)

                with col3:
                    # Natural language query input for selective import
                    nl_query_key = f"nl_query_{schema}_{view_name}_view"
                    nl_query = st.text_input(
                        "Natural Language Import Query (optional)",
                        value=st.session_state.get(nl_query_key, ""),
                        key=f"nl_query_input_{schema}_{view_name}_view"
                    )
                    st.session_state[nl_query_key] = nl_query
                    # Import button
                    if st.button("🚀 Start Import", key=f"import_{schema}_{view_name}_view", type="primary", use_container_width=True):
                        st.session_state.active_db_tab = 1  # Stay on Views tab
                        perform_dataset_import(oracle_manager, schema, view_name, nl_query)
                st.markdown("---")

    def show_table_preview(connection_name: str, schema: str, table_name: str):
        """Show table preview optimized for speed."""
        try:
            with st.container():
                loading_placeholder = st.empty()
                loading_placeholder.info("⚡ Loading preview data...")
                
                preview_data = get_cached_table_preview(connection_name, schema, table_name, 100) 
                loading_placeholder.empty()
                
                if preview_data is not None and not preview_data.is_empty():
                    col1, col2 = st.columns(2)
                    with col1:
                        st.metric("Preview Rows", len(preview_data))
                    with col2:
                        st.metric("Columns", len(preview_data.columns))
                    
                    st.dataframe(preview_data, use_container_width=True, height=250)
                else:
                    st.warning("⚠️ No data found in this table or preview failed to load.")
                    
        except Exception as e:
            st.error(f"❌ Failed to load preview: {e}")
            log_message(f"Preview error: {e}", level="error")

    def perform_dataset_import(oracle_manager, schema, table_name, nl_query=None):
        """Perform dataset import with progress focused on row fetching. Optionally use a natural language query to filter rows."""
        try:
            st.warning("💾 Avoid changing page or clicking buttons during import")
            progress_bar = st.progress(0)
            status_text = st.empty()
            query_text = st.empty()
            stop_button = st.empty()
            st.session_state.stop_import = False

            # Quick connection check
            if not oracle_manager.test_connection():
                st.error("❌ Database connection failed!")
                return

            # Get table row count for progress calculation
            try:
                total_rows = oracle_manager.get_table_count(schema, table_name)
            except Exception as e:
                st.warning(f"⚠️ Could not determine table size: {e}")
                total_rows = 0

            # Progress callback focused on row fetching
            def progress_callback(current_rows, total_rows, status):
                if total_rows > 0:
                    fetch_progress = int((current_rows / total_rows) * 100)
                    progress_bar.progress(fetch_progress)
                    status_text.text(f"📥 Fetching rows: {current_rows:,} / {total_rows:,} ({fetch_progress}%)")
                else:
                    status_text.text(f"📥 Fetching rows: {current_rows:,} rows retrieved...")

            # --- Memory-efficient import logic: stream chunks to Parquet, avoid full DataFrame in RAM ---
            status_text.text("🚀 Starting data fetch...")
            chunk_size = 10_000  # Even lower chunk size for very low RAM
            dataset_name = schema + "_" + table_name
            original_filename = f"{schema}.{table_name}.oracle_import"
            dataset_id = f"{dataset_name}_{int(time.time())}"

            file_storage = get_file_storage()
            import os
            import json
            import shutil
            import tempfile
            import polars as pl
            import pyarrow as pa
            import pyarrow.parquet as pq
            dataset_dir = os.path.join(file_storage.data_dir, dataset_id)
            os.makedirs(dataset_dir, exist_ok=True)
            data_file = os.path.join(dataset_dir, "data.parquet")

            import gc
            temp_parquet = tempfile.NamedTemporaryFile(delete=False, suffix=".parquet")
            parquet_writer = None
            total_rows_imported = 0
            sample_rows = []
            sample_rows_needed = 100
            sample_rows_collected = 0

            # --- Azure OpenAI SQL generation for natural language query ---
            sql_where = ""
            if nl_query and nl_query.strip():
                try:
                    import openai
                    azure_endpoint = os.getenv("AZURE_ENDPOINT")
                    azure_api_key = os.getenv("AZURE_API_TOKEN")
                    azure_deployment = os.getenv("AZURE_DEPLOYMENT_NAME")
                    azure_api_version = os.getenv("AZURE_API_VERSION")
                    if not (azure_endpoint and azure_api_key and azure_deployment and azure_api_version):
                        raise Exception("Azure OpenAI environment variables not set (AZURE_ENDPOINT, AZURE_API_TOKEN, AZURE_DEPLOYMENT_NAME, AZURE_API_VERSION)")
                    client = openai.AzureOpenAI(
                        api_key=azure_api_key,
                        api_version=azure_api_version,
                        azure_endpoint=azure_endpoint
                    )
                    preview_query = f"SELECT * FROM {schema}.{table_name} WHERE ROWNUM <= 1"
                    preview_df = oracle_manager.execute_query(preview_query, limit=1)
                    if hasattr(preview_df, 'columns'):
                        columns = list(preview_df.columns)
                    else:
                        columns = []
                    columns_str = ", ".join(columns)
                    prompt = (
                        f"You are a helpful assistant that writes SQL WHERE clauses for Oracle. "
                        f"Given the table {schema}.{table_name} with columns: {columns_str}, "
                        f"write a SQL WHERE clause (do not include the word 'WHERE' if not needed) for this user request: {nl_query}. "
                        f"If the request is to import the whole table, return an empty string."
                    )
                    response = client.chat.completions.create(
                        model=azure_deployment,
                        messages=[{"role": "user", "content": prompt}],
                        temperature=0,
                        max_tokens=128
                    )
                    sql_where = response.choices[0].message.content.strip()
                    if sql_where and not sql_where.upper().startswith("WHERE"):
                        sql_where = "WHERE " + sql_where
                    status_text.text(f"🤖 Azure OpenAI-generated filter: {sql_where}")
                except Exception as e:
                    st.warning(f"⚠️ Could not generate SQL from natural language query: {e}")
                    sql_where = ""
            try:
                offset = 0
                max_iterations = 100000  # Safety: prevent infinite loop
                prev_first_row = None
                order_by_col = None
                try:
                    col_query = f"SELECT * FROM {schema}.{table_name} WHERE ROWNUM <= 1"
                    col_sample = oracle_manager.execute_query(col_query, limit=1)
                    if col_sample is not None and hasattr(col_sample, 'columns'):
                        order_by_col = col_sample.columns[0]
                except Exception:
                    pass
                if not order_by_col:
                    order_by_col = '1'  # fallback: ORDER BY 1
                iteration = 0
                stop_button.button("🛑 Stop Import", key="stop_import_btn")
                while True:
                    base_query = f"SELECT * FROM {schema}.{table_name} "
                    if sql_where:
                        base_query += sql_where + " "
                    query = base_query + f"ORDER BY {order_by_col} OFFSET {offset} ROWS FETCH NEXT {chunk_size} ROWS ONLY"
                    query_text.info(f"Current SQL: {query}")
                    if st.session_state.get("stop_import", False):
                        status_text.text("🛑 Import stopped by user.")
                        log_message("Import stopped by user.", level="warning")
                        st.session_state.stop_import = False
                        break
                    chunk = oracle_manager.execute_query(query, limit=chunk_size)
                    if chunk is None or len(chunk) == 0:
                        break
                    # Convert to Arrow Table directly for Parquet write, avoid full DataFrame in RAM
                    if hasattr(chunk, 'to_arrow'):
                        table = chunk.to_arrow()
                    else:
                        # Fallback: convert to Arrow Table from dicts
                        import pyarrow as pa
                        table = pa.Table.from_pylist(chunk.to_dicts())
                    if parquet_writer is None:
                        parquet_writer = pq.ParquetWriter(temp_parquet.name, table.schema)
                    parquet_writer.write_table(table)
                    total_rows_imported += table.num_rows
                    offset += chunk_size
                    iteration += 1
                    # Collect sample rows for preview (first 100 rows only, then discard)
                    if sample_rows_collected < sample_rows_needed:
                        rows_to_add = min(sample_rows_needed - sample_rows_collected, table.num_rows)
                        if hasattr(chunk, 'head'):
                            sample_rows.extend(chunk.head(rows_to_add).to_dicts())
                        else:
                            sample_rows.extend([dict(zip(table.column_names, row)) for row in table.to_pylist()[:rows_to_add]])
                        sample_rows_collected += rows_to_add
                    # Infinite loop protection: check if first row is same as previous
                    if table.num_rows > 0:
                        first_row_tuple = tuple(table.to_pydict()[col][0] for col in table.column_names)
                    else:
                        first_row_tuple = None
                    if prev_first_row is not None and first_row_tuple == prev_first_row:
                        status_text.text("⚠️ Import stopped: detected repeated chunk (possible infinite loop). Please check table structure.")
                        log_message("Import stopped: repeated chunk detected.", level="error")
                        break
                    prev_first_row = first_row_tuple
                    progress_callback(total_rows_imported, total_rows, f"Fetched {total_rows_imported:,} rows (offset={offset})...")
                    del chunk
                    del table
                    gc.collect()
                    if iteration > max_iterations:
                        status_text.text("⚠️ Import stopped: exceeded max chunk iterations (possible infinite loop). Please check table structure.")
                        log_message("Import stopped: max iterations exceeded.", level="error")
                        break
                if parquet_writer is not None:
                    parquet_writer.close()
                temp_parquet.close()  # Close the NamedTemporaryFile handle
                shutil.move(temp_parquet.name, data_file)
            except Exception as e:
                st.error(f"❌ Import failed: {str(e)}")
                log_message(f"Dataset import failed: {str(e)}", level="error")
                import traceback
                log_message(traceback.format_exc(), level="error")
                if parquet_writer is not None:
                    parquet_writer.close()
                try:
                    temp_parquet.close()
                except Exception:
                    pass
                if os.path.exists(temp_parquet.name):
                    try:
                        os.remove(temp_parquet.name)
                    except Exception:
                        pass
                return


            # Calculate file size
            stored_file_size_bytes = os.path.getsize(data_file)
            stored_file_size_mb = stored_file_size_bytes / (1024 * 1024)

            # Only load a tiny sample for metadata
            if len(sample_rows) > 0:
                sample_df = pl.DataFrame(sample_rows)
            else:
                sample_df = pl.read_parquet(data_file, n_rows=10)

            column_info = file_storage._generate_column_info(sample_df)
            memory_usage_mb = sample_df.estimated_size() / (1024 * 1024)

            metadata = {
                'dataset_id': dataset_id,
                'dataset_name': dataset_name,
                'original_filename': original_filename,
                'stored_at': time.time(),
                'created_at': time.strftime('%Y-%m-%d %H:%M:%S'),
                'shape': (total_rows_imported, len(sample_df.columns)),
                'columns': sample_df.columns,
                'dtypes': {col: str(sample_df[col].dtype) for col in sample_df.columns},
                'column_info': column_info,
                'file_size_mb': stored_file_size_mb,
                'memory_usage_mb': memory_usage_mb,
                'memory_mb': memory_usage_mb,
                'original_file_size_mb': stored_file_size_mb,  # No original file, so use stored
                'file_size_bytes': stored_file_size_bytes,
                'sample_data': {
                    'sample_rows': sample_rows,
                    'sample_size': len(sample_rows)
                }
            }

            # Store metadata
            metadata_file = os.path.join(file_storage.metadata_dir, f"{dataset_id}.json")
            with open(metadata_file, 'w') as f:
                json.dump(metadata, f, indent=2, default=str)

            progress_bar.progress(100)
            status_text.text("💾 Storing dataset...")

            # Register only a lazy reference to the dataset, not the full DataFrame, to avoid RAM usage
            try:
                # Do NOT load the full dataset into RAM
                store_dataset(
                    dataset_name=dataset_name,
                    df=None,  # Indicate lazy loading or use a custom lazy loader if available
                    original_filename=original_filename,
                    file_size_bytes=stored_file_size_bytes,
                    parquet_path=data_file
                )
            except Exception as e:
                st.warning(f"⚠️ Dataset imported but could not be registered for use: {e}")
                log_message(f"Dataset import: failed to register dataset: {e}", level="error")

            st.success(
                f"🎉 **Dataset Import Successful!**\n\n"
                f"• **Dataset Name:** `{dataset_name}`\n"
                f"• **Source:** `{schema}.{table_name}`\n"
                f"• **Rows Imported:** {total_rows_imported:,}\n"
                f"• **Columns:** {len(sample_df.columns)}\n"
                f"• **Size:** {stored_file_size_mb:.1f} MB\n"
                f"• **Dataset ID:** `{dataset_id}`"
            )

        except Exception as e:
            st.error(f"❌ Import failed: {str(e)}")
            log_message(f"Dataset import failed: {str(e)}", level="error")
            import traceback
            log_message(traceback.format_exc(), level="error")

    # Main page layout
    def main():
        """Main function to render the Database Connections page."""
        # Initialize session state
        initialize_db_session_state()
        
        # Render sidebar
        render_sidebar()
        
        # Main page header
        st.title("🗄️ Database Connections")
        st.markdown("Connect to Oracle databases, explore schemas and tables, then import datasets for analysis.")

        render_database_explorer()

    # Run the main function
    if __name__ == "__main__":
        main()
