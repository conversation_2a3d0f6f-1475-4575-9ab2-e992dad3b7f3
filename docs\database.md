# Database Integration

Pandas-AI provides robust integration with Oracle databases, allowing users to fetch and analyze data directly from their database instances.

## Connection Methods

There is at the moment only one way to connect to an Oracle database in Pandas-AI:

1. **Environment Variables**: For automated setups or Docker deployments, you can configure the database connection using environment variables. These can be set in your operating system or in a `.env` file in the project root.

## Oracle Instant Client

The application uses the `oracledb` Python driver to connect to Oracle databases. For this to work, the Oracle Instant Client libraries need to be available on the system.

- **Local Setup**: If you are running the application locally, you will need to download and install the Oracle Instant Client for your operating system. Make sure to add the client directory to your system's `PATH` or set the `ORACLE_CLIENT_LIB` environment variable.
- **Docker Setup**: The provided `Dockerfile` automatically downloads and configures the Oracle Instant Client within the container, so no additional setup is required when using Docker.

## Oracle Wallet

For secure connections, especially to Oracle Autonomous Databases, the application supports the use of an Oracle Wallet.

- Place your wallet files (e.g., `cwallet.sso`, `tnsnames.ora`, `sqlnet.ora`) in the `wallet/` directory at the project root.
- The `Dockerfile` is configured to copy these files to the appropriate location within the container.
- When running locally, you may need to set the `TNS_ADMIN` environment variable to the path of your `wallet` directory.

## Configuration

The connection is managed by the `OracleManager` class in `app_core/database/oracle_manager.py`. This class handles the logic for connecting to the database, fetching data, and managing connection pools.

For a detailed list of all supported environment variables for database configuration, please see the [Environment Variables](env_vars.md) documentation.

---

Next, learn about the [Application Pages](pages.md).
