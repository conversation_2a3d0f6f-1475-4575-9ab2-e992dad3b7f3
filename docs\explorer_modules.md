# Explorer Modules

Explorer modules extend the Data Explorer page with advanced data analysis and visualization tools.

## Available Modules
- **Data Explorer Tab**: Main interface for browsing and interacting with datasets.
- **Data Profiling**: Generate detailed statistics and profiles for datasets.
- **Data Quality**: Assess and visualize data quality metrics.
- **Dataset Comparison**: Compare two or more datasets for differences and similarities.
- **Visual Explorer**: Create advanced visualizations and charts.

Each module is accessible from the Data Explorer page and provides specialized tools for deeper analysis.

---

## Developer Notes

- **Modular Architecture**: Each module is implemented as a separate Python file/class, imported and registered in `explorer_modules/__init__.py`.
- **Loose Coupling**: Modules interact with the main app via well-defined interfaces, making it easy to add, remove, or update modules.
- **Extensibility**: New modules can be added by creating a new file and updating the registry in `__init__.py`.
