# 1. Version control
.git/
.gitignore
.gitattributes

# 2. Python artifacts
__pycache__/
*.pyc
*.pyo
*.pyd
.pytest_cache/
.coverage

# 3. Virtual environments
.venv/
venv/
ENV/

# 4. IDE/editor files
.vscode/
.idea/
*.swp
*.swo

# 5. Build and distribution outputs
dist/
build/
out/

# 6. Test and debug artifacts
test/
tests/
*.log
*.tmp
tmp/
temp/

# 7. Node.js (not detected, but common)
node_modules/
npm-debug.log
yarn-debug.log

# 8. Go (not detected, but common)
/vendor/
*.test
.go-cache/

# 9. Java (not detected, but common)
target/
*.class
.gradle/

# 10. Environment and secrets
# .env*
# *.env
# *.pem
# *.key
# *.crt

# 11. Project-specific and documentation
docs/
README*
*.md

# 12. Docker and compose files (do NOT ignore Dockerfile itself)
docker-compose*

# 13. Configuration overrides
config.local.*
*.local.yml
.local/
local/

# 14. Miscellaneous
*.bak
*.old

# 15. Exports and logs
# exports/
# logs/

# 16. Empty data directories (keep if needed, but ignore contents)
data/*
!data/.gitkeep

# 17. Ignore certificate files in certs/
# certs/*.pem
# certs/*.key
# certs/*.crt

# 18. Scripts cache
scripts/__pycache__/

# 19. Other temporary or backup files
*~
