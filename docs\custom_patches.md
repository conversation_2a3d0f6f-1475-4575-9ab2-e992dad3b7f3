# Custom Patches

Pandas-AI includes several custom patches to extend and modify the behavior of the PandasAI library. These patches are located in the `app_core/custom_patches/` directory and provide enhanced functionality for handling responses, parsing results, and ensuring proper chart rendering.

## Overview

The custom patches serve the following purposes:

1. **Enhanced Response Handling**: Support for multiple results in a single response
2. **Improved Result Parsing**: Extended parsing capabilities for various data types
3. **Chart Display Fixes**: Ensures proper Plotly chart rendering in Streamlit

## Patch Modules

### 1. Multiple Response Handler (`multiple.py`)

This patch extends the PandasAI response system to handle multiple results in a single response.

#### Purpose
- Allows the AI to return multiple outputs (charts, tables, text) from a single query
- Maintains compatibility with existing single-result responses
- Provides convenient methods to access and manipulate multiple results

#### Key Features
- **MultipleResponse Class**: Extends `BaseResponse` to handle lists of results
- **Result Access**: Methods to get results by index or type
- **String Representation**: Combines all results into a readable format
- **Validation**: Ensures all results have the required structure

#### Usage Example
```python
from app_core.custom_patches.multiple import MultipleResponse

# Create a response with multiple results
values = [
    {"type": "string", "value": "Analysis summary"},
    {"type": "dataframe", "value": df},
    {"type": "plot", "value": chart_data}
]
response = MultipleResponse(values, last_code_executed="...")
```

### 2. Enhanced Response Parser (`parser.py`)

This patch extends the default PandasAI response parser to handle additional data types and validation scenarios.

#### Purpose
- Supports parsing of multiple result types in a single response
- Provides more robust validation for different output formats
- Handles edge cases in data type validation

#### Key Features
- **Extended Type Support**: Handles `html`, `figure`, `text`, and `multiple` result types
- **Flexible Plot Validation**: More permissive validation for chart objects
- **Multiple Result Parsing**: Automatically detects and handles lists of results
- **Error Handling**: Comprehensive validation with detailed error messages

#### Supported Result Types
- `number`: Numeric values (int, float, numpy types)
- `string`/`text`: Text content
- `dataframe`: Pandas DataFrames and Series
- `plot`: Chart objects (Plotly figures, base64 images, file paths)
- `html`: HTML content for rich display
- `figure`: Generic figure objects (Plotly, Matplotlib)
- `multiple`: Lists containing multiple result objects

#### Usage Example
```python
from app_core.custom_patches.parser import ResponseParser

parser = ResponseParser()

# Parse a single result
result = {"type": "dataframe", "value": df}
response = parser.parse(result, "df.head()")

# Parse multiple results
results = [
    {"type": "string", "value": "Summary"},
    {"type": "plot", "value": plotly_fig}
]
response = parser.parse(results, "analysis_code")
```

### 3. Plotly Chart Fix (`plotly_fix.py`)

This patch ensures that charts are properly rendered using Plotly in the Streamlit environment.

#### Purpose
- Forces PandasAI to use Plotly for all chart generation
- Prevents GUI window pop-ups in server environments
- Configures proper rendering for Streamlit compatibility

#### Key Features
- **Plotly Enforcement**: Patches PandasAI prompts to always request Plotly charts
- **Environment Configuration**: Sets appropriate environment variables
- **Renderer Setup**: Configures Plotly to use Streamlit-compatible renderers
- **One-time Application**: Ensures patches are applied only once

#### Technical Details
- Patches `pandasai.core.prompts.get_chat_prompt_for_sql` function
- Sets `viz_lib`, `plotting_backend`, and `preferred_plotting_library` to 'plotly'
- Configures pandas plotting backend to use Plotly
- Sets appropriate Plotly renderer (streamlit, browser, or fallback)

#### Usage
The patch is automatically applied when imported:
```python
from app_core.custom_patches.plotly_fix import apply_plotly_patches

# Apply the patches (typically done during app initialization)
apply_plotly_patches()
```

## Integration with Main Application

### Initialization
The patches are typically applied during application startup to ensure all PandasAI interactions use the enhanced functionality.

### Response Handling
The enhanced parser is used throughout the application to handle AI responses, ensuring that all result types are properly validated and formatted.

### Chart Rendering
The Plotly fix ensures that all charts generated by the AI are properly displayed in the Streamlit interface without compatibility issues.

## Benefits

1. **Enhanced User Experience**: Multiple results can be displayed from a single AI query
2. **Robust Error Handling**: Better validation prevents application crashes from malformed responses
3. **Consistent Chart Display**: All charts render properly in the web interface
4. **Extensibility**: Easy to add new result types and validation rules

## Development Notes

### Adding New Result Types
To add support for a new result type:

1. Add validation logic in `parser.py` in the `_validate_response` method
2. Add response generation logic in the `_generate_response` method
3. Create a new response class if needed (following the pattern of existing classes)

### Debugging Patches
- Check the application logs for patch application messages
- Verify that `_patches_applied` flag is set in `plotly_fix.py`
- Test with simple queries to ensure patches are working correctly

### Testing
When testing the patches:
- Test single and multiple result scenarios
- Verify chart rendering across different browsers
- Check error handling with malformed responses
- Ensure backward compatibility with existing code

---

For more information about the application structure, see [Application Structure](structure.md).
